const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User');
const Student = require('../models/Student');
const Class = require('../models/Class');
const Subscription = require('../models/Subscription');
const Attendance = require('../models/Attendance');
const Payment = require('../models/Payment');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/taekwondo_crm', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('تم الاتصال بقاعدة البيانات بنجاح');
  } catch (error) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error);
    process.exit(1);
  }
};

// Clear existing data
const clearDatabase = async () => {
  try {
    await User.deleteMany({});
    await Student.deleteMany({});
    await Class.deleteMany({});
    await Subscription.deleteMany({});
    await Attendance.deleteMany({});
    await Payment.deleteMany({});
    console.log('تم مسح البيانات الموجودة');
  } catch (error) {
    console.error('خطأ في مسح البيانات:', error);
  }
};

// Seed users
const seedUsers = async () => {
  try {
    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        fullName: 'مدير النظام',
        role: 'admin',
        phone: '+966501234567',
        permissions: ['read_students', 'write_students', 'read_attendance', 'write_attendance', 
                     'read_subscriptions', 'write_subscriptions', 'read_reports', 'manage_users']
      },
      {
        username: 'coach1',
        email: '<EMAIL>',
        password: 'coach123',
        fullName: 'أحمد المدرب',
        role: 'coach',
        phone: '+966507654321',
        permissions: ['read_students', 'write_students', 'read_attendance', 'write_attendance', 'read_subscriptions']
      },
      {
        username: 'coach2',
        email: '<EMAIL>',
        password: 'coach123',
        fullName: 'فاطمة المدربة',
        role: 'coach',
        phone: '+966509876543',
        permissions: ['read_students', 'write_students', 'read_attendance', 'write_attendance', 'read_subscriptions']
      },
      {
        username: 'receptionist',
        email: '<EMAIL>',
        password: 'reception123',
        fullName: 'سارة الاستقبال',
        role: 'receptionist',
        phone: '+966502468135',
        permissions: ['read_students', 'write_students', 'read_subscriptions', 'write_subscriptions']
      }
    ];

    const createdUsers = [];
    for (const userData of users) {
      const user = new User(userData);
      await user.save();
      createdUsers.push(user);
    }
    console.log(`تم إنشاء ${createdUsers.length} مستخدم`);
    return createdUsers;
  } catch (error) {
    console.error('خطأ في إنشاء المستخدمين:', error);
    return [];
  }
};

// Seed classes
const seedClasses = async (users) => {
  try {
    const coaches = users.filter(user => user.role === 'coach');
    
    const classes = [
      {
        name: 'صف المبتدئين الصباحي',
        type: 'beginner',
        level: 'white',
        instructor: coaches[0]._id,
        schedule: {
          dayOfWeek: 1, // Monday
          startTime: '09:00',
          endTime: '10:00'
        },
        capacity: 15,
        ageRange: { min: 6, max: 12 },
        fees: { monthly: 200, perSession: 25 },
        description: 'صف للمبتدئين من عمر 6 إلى 12 سنة'
      },
      {
        name: 'صف المتوسط المسائي',
        type: 'intermediate',
        level: 'yellow',
        instructor: coaches[1]._id,
        schedule: {
          dayOfWeek: 2, // Tuesday
          startTime: '17:00',
          endTime: '18:30'
        },
        capacity: 12,
        ageRange: { min: 10, max: 16 },
        fees: { monthly: 250, perSession: 30 },
        description: 'صف للمستوى المتوسط'
      },
      {
        name: 'صف البالغين',
        type: 'adults',
        level: 'mixed',
        instructor: coaches[0]._id,
        schedule: {
          dayOfWeek: 3, // Wednesday
          startTime: '19:00',
          endTime: '20:30'
        },
        capacity: 10,
        ageRange: { min: 18, max: 50 },
        fees: { monthly: 300, perSession: 40 },
        description: 'صف للبالغين جميع المستويات'
      }
    ];

    const createdClasses = await Class.insertMany(classes);
    console.log(`تم إنشاء ${createdClasses.length} صف`);
    return createdClasses;
  } catch (error) {
    console.error('خطأ في إنشاء الصفوف:', error);
    return [];
  }
};

// Seed students
const seedStudents = async (users) => {
  try {
    const admin = users.find(user => user.role === 'admin');
    
    const students = [
      {
        studentId: 'TKD20240001',
        firstName: 'أحمد',
        lastName: 'محمد',
        dateOfBirth: new Date('2010-05-15'),
        gender: 'male',
        phone: '+966551234567',
        email: '<EMAIL>',
        address: {
          street: 'شارع الملك فهد',
          city: 'الرياض',
          state: 'الرياض',
          zipCode: '12345',
          country: 'السعودية'
        },
        emergencyContact: {
          name: 'محمد أحمد',
          relationship: 'والد',
          phone: '+966501234567'
        },
        beltLevel: 'أصفر',
        createdBy: admin._id
      },
      {
        studentId: 'TKD20240002',
        firstName: 'فاطمة',
        lastName: 'علي',
        dateOfBirth: new Date('2012-08-22'),
        gender: 'female',
        phone: '+966557654321',
        email: '<EMAIL>',
        address: {
          street: 'شارع العليا',
          city: 'الرياض',
          state: 'الرياض',
          zipCode: '12346',
          country: 'السعودية'
        },
        emergencyContact: {
          name: 'علي فاطمة',
          relationship: 'والد',
          phone: '+966507654321'
        },
        beltLevel: 'أخضر',
        createdBy: admin._id
      },
      {
        studentId: 'TKD20240003',
        firstName: 'خالد',
        lastName: 'السعد',
        dateOfBirth: new Date('2008-12-10'),
        gender: 'male',
        phone: '+966559876543',
        email: '<EMAIL>',
        address: {
          street: 'شارع التحلية',
          city: 'الرياض',
          state: 'الرياض',
          zipCode: '12347',
          country: 'السعودية'
        },
        emergencyContact: {
          name: 'سعد خالد',
          relationship: 'والد',
          phone: '+966509876543'
        },
        beltLevel: 'أزرق',
        createdBy: admin._id
      },
      {
        studentId: 'TKD20240004',
        firstName: 'نورا',
        lastName: 'الأحمد',
        dateOfBirth: new Date('2011-03-18'),
        gender: 'female',
        phone: '+966552468135',
        email: '<EMAIL>',
        address: {
          street: 'شارع الأمير سلطان',
          city: 'الرياض',
          state: 'الرياض',
          zipCode: '12348',
          country: 'السعودية'
        },
        emergencyContact: {
          name: 'أحمد نورا',
          relationship: 'والد',
          phone: '+966502468135'
        },
        beltLevel: 'برتقالي',
        createdBy: admin._id
      },
      {
        studentId: 'TKD20240005',
        firstName: 'عبدالله',
        lastName: 'المطيري',
        dateOfBirth: new Date('1995-07-25'),
        gender: 'male',
        phone: '+966558642097',
        email: '<EMAIL>',
        address: {
          street: 'شارع الملك عبدالعزيز',
          city: 'الرياض',
          state: 'الرياض',
          zipCode: '12349',
          country: 'السعودية'
        },
        emergencyContact: {
          name: 'مطيري عبدالله',
          relationship: 'أخ',
          phone: '+966508642097'
        },
        beltLevel: 'بني',
        createdBy: admin._id
      }
    ];

    const createdStudents = await Student.insertMany(students);
    console.log(`تم إنشاء ${createdStudents.length} طالب`);
    return createdStudents;
  } catch (error) {
    console.error('خطأ في إنشاء الطلاب:', error);
    return [];
  }
};

module.exports = {
  connectDB,
  clearDatabase,
  seedUsers,
  seedClasses,
  seedStudents
};
