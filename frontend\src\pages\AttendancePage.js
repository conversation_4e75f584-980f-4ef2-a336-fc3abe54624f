// Attendance Management Page
class AttendancePage {
  constructor() {
    this.state = window.stateManager;
    this.api = window.apiServices;
    this.utils = window.utils;
    this.currentPage = 1;
    this.pageSize = 10;
    this.searchQuery = '';
    this.filters = {};
    this.sortBy = 'date';
    this.sortOrder = 'desc';
    this.attendance = [];
    this.totalAttendance = 0;
    this.selectedDate = new Date().toISOString().split('T')[0];
    this.students = [];
    this.instructors = [];
    this.isLoading = false;
  }

  async render(container) {
    container.innerHTML = `
      <div class="attendance-page">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h3 class="mb-1">إدارة الحضور</h3>
            <p class="text-muted mb-0">تسجيل ومتابعة حضور الطلاب</p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" id="attendanceReportBtn">
              <i class="fas fa-chart-bar me-2"></i>تقرير الحضور
            </button>
            <button class="btn btn-outline-secondary" id="bulkAttendanceBtn">
              <i class="fas fa-users me-2"></i>تسجيل جماعي
            </button>
            <button class="btn btn-primary" id="markAttendanceBtn">
              <i class="fas fa-plus me-2"></i>تسجيل حضور
            </button>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-success bg-gradient rounded-circle p-3">
                      <i class="fas fa-user-check text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">حضور اليوم</div>
                    <div class="h4 mb-0" id="todayPresentCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-danger bg-gradient rounded-circle p-3">
                      <i class="fas fa-user-times text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">غياب اليوم</div>
                    <div class="h4 mb-0" id="todayAbsentCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-warning bg-gradient rounded-circle p-3">
                      <i class="fas fa-clock text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">تأخير اليوم</div>
                    <div class="h4 mb-0" id="todayLateCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-info bg-gradient rounded-circle p-3">
                      <i class="fas fa-percentage text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">معدل الحضور</div>
                    <div class="h4 mb-0" id="attendanceRate">0%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Filters and Search -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">التاريخ</label>
                <input type="date" class="form-control" id="dateFilter" value="${this.selectedDate}">
              </div>
              <div class="col-md-3">
                <label class="form-label">نوع الصف</label>
                <select class="form-select" id="classTypeFilter">
                  <option value="">جميع الصفوف</option>
                  <option value="beginner">مبتدئ</option>
                  <option value="intermediate">متوسط</option>
                  <option value="advanced">متقدم</option>
                  <option value="competition">منافسات</option>
                  <option value="private">خاص</option>
                </select>
              </div>
              <div class="col-md-3">
                <label class="form-label">حالة الحضور</label>
                <select class="form-select" id="statusFilter">
                  <option value="">جميع الحالات</option>
                  <option value="present">حاضر</option>
                  <option value="absent">غائب</option>
                  <option value="late">متأخر</option>
                  <option value="excused">معذور</option>
                </select>
              </div>
              <div class="col-md-3">
                <label class="form-label">البحث</label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input type="text" class="form-control" id="searchInput"
                         placeholder="البحث بالاسم أو رقم الطالب...">
                </div>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-12">
                <button class="btn btn-outline-secondary" id="clearFiltersBtn">
                  <i class="fas fa-times me-2"></i>مسح الفلاتر
                </button>
                <button class="btn btn-outline-primary ms-2" id="exportAttendanceBtn">
                  <i class="fas fa-download me-2"></i>تصدير البيانات
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Attendance Table -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">سجلات الحضور</h5>
            <div class="d-flex align-items-center gap-3">
              <div class="d-flex align-items-center">
                <label class="form-label mb-0 me-2">عرض:</label>
                <select class="form-select form-select-sm" id="pageSizeSelect" style="width: auto;">
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>الطالب</th>
                    <th>التاريخ</th>
                    <th>نوع الصف</th>
                    <th>الحالة</th>
                    <th>وقت الدخول</th>
                    <th>وقت الخروج</th>
                    <th>المدرب</th>
                    <th>التقييم</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody id="attendanceTableBody">
                  <!-- Attendance records will be loaded here -->
                </tbody>
              </table>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-5 d-none">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
              </div>
              <p class="mt-3 text-muted">جاري تحميل سجلات الحضور...</p>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="text-center py-5 d-none">
              <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا توجد سجلات حضور</h5>
              <p class="text-muted">لم يتم العثور على سجلات حضور للفترة المحددة</p>
              <button class="btn btn-primary" id="addFirstAttendanceBtn">
                <i class="fas fa-plus me-2"></i>تسجيل أول حضور
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div class="text-muted">
                عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span>
                من أصل <span id="totalCount">0</span> سجل
              </div>
              <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                  <!-- Pagination will be generated here -->
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    `;

    await this.bindEvents();
    await this.loadInitialData();
    await this.loadAttendance();
    await this.loadTodayStats();
  }

  async bindEvents() {
    // Date filter change
    const dateFilter = document.getElementById('dateFilter');
    if (dateFilter) {
      dateFilter.addEventListener('change', (e) => {
        this.selectedDate = e.target.value;
        this.currentPage = 1;
        this.loadAttendance();
        this.loadTodayStats();
      });
    }

    // Search input with debounce
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.searchQuery = e.target.value;
          this.currentPage = 1;
          this.loadAttendance();
        }, 300);
      });
    }

    // Filter changes
    ['classTypeFilter', 'statusFilter'].forEach(filterId => {
      const filter = document.getElementById(filterId);
      if (filter) {
        filter.addEventListener('change', (e) => {
          const filterKey = filterId.replace('Filter', '');
          if (e.target.value) {
            this.filters[filterKey] = e.target.value;
          } else {
            delete this.filters[filterKey];
          }
          this.currentPage = 1;
          this.loadAttendance();
        });
      }
    });

    // Clear filters
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', () => {
        this.clearFilters();
      });
    }

    // Page size change
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
      pageSizeSelect.addEventListener('change', (e) => {
        this.pageSize = parseInt(e.target.value);
        this.currentPage = 1;
        this.loadAttendance();
      });
    }

    // Action buttons
    const markAttendanceBtn = document.getElementById('markAttendanceBtn');
    const addFirstAttendanceBtn = document.getElementById('addFirstAttendanceBtn');
    const bulkAttendanceBtn = document.getElementById('bulkAttendanceBtn');
    const attendanceReportBtn = document.getElementById('attendanceReportBtn');
    const exportAttendanceBtn = document.getElementById('exportAttendanceBtn');

    if (markAttendanceBtn) {
      markAttendanceBtn.addEventListener('click', () => this.showAttendanceModal());
    }
    if (addFirstAttendanceBtn) {
      addFirstAttendanceBtn.addEventListener('click', () => this.showAttendanceModal());
    }
    if (bulkAttendanceBtn) {
      bulkAttendanceBtn.addEventListener('click', () => this.showBulkAttendanceModal());
    }
    if (attendanceReportBtn) {
      attendanceReportBtn.addEventListener('click', () => this.showAttendanceReport());
    }
    if (exportAttendanceBtn) {
      exportAttendanceBtn.addEventListener('click', () => this.exportAttendance());
    }

    // Table row actions (using event delegation)
    const tableBody = document.getElementById('attendanceTableBody');
    if (tableBody) {
      tableBody.addEventListener('click', (e) => {
        const attendanceId = e.target.closest('tr')?.dataset.attendanceId;
        if (!attendanceId) return;

        if (e.target.closest('.btn-view')) {
          this.viewAttendance(attendanceId);
        } else if (e.target.closest('.btn-edit')) {
          this.editAttendance(attendanceId);
        } else if (e.target.closest('.btn-delete')) {
          this.deleteAttendance(attendanceId);
        } else if (e.target.closest('.btn-checkout')) {
          this.checkoutStudent(attendanceId);
        }
      });
    }

    // Pagination (using event delegation)
    const pagination = document.getElementById('pagination');
    if (pagination) {
      pagination.addEventListener('click', (e) => {
        e.preventDefault();
        const page = e.target.dataset.page;
        if (page && page !== this.currentPage.toString()) {
          this.currentPage = parseInt(page);
          this.loadAttendance();
        }
      });
    }
  }

  clearFilters() {
    this.searchQuery = '';
    this.filters = {};
    this.currentPage = 1;
    this.selectedDate = new Date().toISOString().split('T')[0];

    // Reset form elements
    document.getElementById('searchInput').value = '';
    document.getElementById('classTypeFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFilter').value = this.selectedDate;

    this.loadAttendance();
    this.loadTodayStats();
  }

  async loadInitialData() {
    try {
      // Load students and instructors for dropdowns
      const [studentsResponse, usersResponse] = await Promise.all([
        this.api.students.getAll({ limit: 1000, status: 'active' }),
        this.api.users.getAll({ limit: 100, role: 'coach' })
      ]);

      if (studentsResponse.success) {
        this.students = studentsResponse.data.students;
      }

      if (usersResponse.success) {
        this.instructors = usersResponse.data.users;
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  }

  async loadAttendance() {
    try {
      this.setLoading(true);

      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        sortBy: this.sortBy,
        sortOrder: this.sortOrder
      };

      if (this.searchQuery) {
        params.search = this.searchQuery;
      }

      if (this.selectedDate) {
        params.startDate = this.selectedDate;
        params.endDate = this.selectedDate;
      }

      Object.assign(params, this.filters);

      const response = await this.api.attendance.getAll(params);

      if (response.success) {
        this.attendance = response.data.attendance;
        this.totalAttendance = response.data.total;
        this.renderAttendanceTable();
        this.renderPagination();
        this.updatePaginationInfo();
      } else {
        this.utils.showAlert('خطأ في تحميل سجلات الحضور', 'error');
      }
    } catch (error) {
      console.error('Error loading attendance:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  async loadTodayStats() {
    try {
      const today = this.selectedDate || new Date().toISOString().split('T')[0];
      const response = await this.api.attendance.getStats({
        startDate: today,
        endDate: today
      });

      if (response.success) {
        const stats = response.data.stats;
        document.getElementById('todayPresentCount').textContent = stats.present || 0;
        document.getElementById('todayAbsentCount').textContent = stats.absent || 0;
        document.getElementById('todayLateCount').textContent = stats.late || 0;
        document.getElementById('attendanceRate').textContent = `${stats.attendanceRate || 0}%`;
      }
    } catch (error) {
      console.error('Error loading today stats:', error);
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    const loadingState = document.getElementById('loadingState');
    const tableBody = document.getElementById('attendanceTableBody');

    if (loading) {
      loadingState?.classList.remove('d-none');
      tableBody?.classList.add('d-none');
    } else {
      loadingState?.classList.add('d-none');
      tableBody?.classList.remove('d-none');
    }
  }

  renderAttendanceTable() {
    const tableBody = document.getElementById('attendanceTableBody');
    const emptyState = document.getElementById('emptyState');

    if (!tableBody) return;

    if (this.attendance.length === 0) {
      tableBody.classList.add('d-none');
      emptyState?.classList.remove('d-none');
      return;
    }

    emptyState?.classList.add('d-none');
    tableBody.classList.remove('d-none');

    tableBody.innerHTML = this.attendance.map(record => `
      <tr data-attendance-id="${record._id}">
        <td>
          <div class="d-flex align-items-center">
            <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
              <i class="fas fa-user text-muted"></i>
            </div>
            <div>
              <div class="fw-medium">${record.student?.fullName || 'غير محدد'}</div>
              <small class="text-muted">${record.student?.studentId || ''}</small>
            </div>
          </div>
        </td>
        <td>
          <div class="fw-medium">${this.utils.formatDate(record.date)}</div>
          <small class="text-muted">${this.utils.formatTime(record.date)}</small>
        </td>
        <td>
          <span class="badge ${this.getClassTypeColor(record.classType)}">
            ${this.getClassTypeText(record.classType)}
          </span>
        </td>
        <td>
          <span class="badge ${this.getStatusColor(record.status)}">
            ${this.getStatusText(record.status)}
          </span>
        </td>
        <td>
          ${record.checkInTime ? `
            <div class="fw-medium">${this.utils.formatTime(record.checkInTime)}</div>
          ` : '<span class="text-muted">-</span>'}
        </td>
        <td>
          ${record.checkOutTime ? `
            <div class="fw-medium">${this.utils.formatTime(record.checkOutTime)}</div>
            <small class="text-muted">${record.sessionDuration || 0} دقيقة</small>
          ` : record.status === 'present' ? `
            <button class="btn btn-sm btn-outline-warning btn-checkout">
              <i class="fas fa-sign-out-alt"></i> خروج
            </button>
          ` : '<span class="text-muted">-</span>'}
        </td>
        <td>
          <div class="fw-medium">${record.instructor?.fullName || 'غير محدد'}</div>
        </td>
        <td>
          ${record.overallPerformance ? `
            <div class="d-flex align-items-center">
              <div class="progress me-2" style="width: 60px; height: 8px;">
                <div class="progress-bar bg-${this.getPerformanceColor(record.overallPerformance)}"
                     style="width: ${record.overallPerformance * 10}%"></div>
              </div>
              <small class="fw-medium">${record.overallPerformance}/10</small>
            </div>
          ` : '<span class="text-muted">-</span>'}
        </td>
        <td>
          <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary btn-view" title="عرض التفاصيل">
              <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-outline-warning btn-edit" title="تعديل">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-outline-danger btn-delete" title="حذف">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `).join('');
  }

  getClassTypeColor(classType) {
    const colors = {
      'beginner': 'bg-success',
      'intermediate': 'bg-warning',
      'advanced': 'bg-danger',
      'competition': 'bg-primary',
      'private': 'bg-info'
    };
    return colors[classType] || 'bg-secondary';
  }

  getClassTypeText(classType) {
    const texts = {
      'beginner': 'مبتدئ',
      'intermediate': 'متوسط',
      'advanced': 'متقدم',
      'competition': 'منافسات',
      'private': 'خاص'
    };
    return texts[classType] || classType;
  }

  getStatusColor(status) {
    const colors = {
      'present': 'bg-success',
      'absent': 'bg-danger',
      'late': 'bg-warning',
      'excused': 'bg-info'
    };
    return colors[status] || 'bg-secondary';
  }

  getStatusText(status) {
    const texts = {
      'present': 'حاضر',
      'absent': 'غائب',
      'late': 'متأخر',
      'excused': 'معذور'
    };
    return texts[status] || status;
  }

  getPerformanceColor(score) {
    if (score >= 8) return 'success';
    if (score >= 6) return 'warning';
    return 'danger';
  }

  renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    const totalPages = Math.ceil(this.totalAttendance / this.pageSize);

    if (totalPages <= 1) {
      pagination.innerHTML = '';
      return;
    }

    let paginationHTML = '';

    // Previous button
    paginationHTML += `
      <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage - 1}">
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
    `;

    // Page numbers
    const startPage = Math.max(1, this.currentPage - 2);
    const endPage = Math.min(totalPages, this.currentPage + 2);

    if (startPage > 1) {
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="1">1</a>
        </li>
      `;
      if (startPage > 2) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
        <li class="page-item ${i === this.currentPage ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
        </li>
      `;
    }

    // Next button
    paginationHTML += `
      <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage + 1}">
          <i class="fas fa-chevron-left"></i>
        </a>
      </li>
    `;

    pagination.innerHTML = paginationHTML;
  }

  updatePaginationInfo() {
    const showingFrom = document.getElementById('showingFrom');
    const showingTo = document.getElementById('showingTo');
    const totalCount = document.getElementById('totalCount');

    if (showingFrom && showingTo && totalCount) {
      const from = (this.currentPage - 1) * this.pageSize + 1;
      const to = Math.min(this.currentPage * this.pageSize, this.totalAttendance);

      showingFrom.textContent = this.totalAttendance > 0 ? from : 0;
      showingTo.textContent = to;
      totalCount.textContent = this.totalAttendance;
    }
  }

  // CRUD Operations
  async showAttendanceModal(attendance = null) {
    const isEdit = !!attendance;
    const modalTitle = isEdit ? 'تعديل سجل الحضور' : 'تسجيل حضور جديد';

    const modalHTML = `
      <div class="modal fade" id="attendanceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${modalTitle}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="attendanceForm" novalidate>
                <div class="row">
                  <!-- Student Selection -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">الطالب <span class="text-danger">*</span></label>
                    <select class="form-select" name="student" required>
                      <option value="">اختر الطالب</option>
                      ${this.students.map(student => `
                        <option value="${student._id}" ${attendance?.student?._id === student._id ? 'selected' : ''}>
                          ${student.fullName} (${student.studentId})
                        </option>
                      `).join('')}
                    </select>
                    <div class="invalid-feedback">يرجى اختيار الطالب</div>
                  </div>

                  <!-- Date -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">التاريخ <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" name="date" required
                           value="${attendance?.date ? new Date(attendance.date).toISOString().split('T')[0] : this.selectedDate}">
                    <div class="invalid-feedback">التاريخ مطلوب</div>
                  </div>

                  <!-- Class Type -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">نوع الصف <span class="text-danger">*</span></label>
                    <select class="form-select" name="classType" required>
                      <option value="">اختر نوع الصف</option>
                      <option value="beginner" ${attendance?.classType === 'beginner' ? 'selected' : ''}>مبتدئ</option>
                      <option value="intermediate" ${attendance?.classType === 'intermediate' ? 'selected' : ''}>متوسط</option>
                      <option value="advanced" ${attendance?.classType === 'advanced' ? 'selected' : ''}>متقدم</option>
                      <option value="competition" ${attendance?.classType === 'competition' ? 'selected' : ''}>منافسات</option>
                      <option value="private" ${attendance?.classType === 'private' ? 'selected' : ''}>خاص</option>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار نوع الصف</div>
                  </div>

                  <!-- Status -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">حالة الحضور <span class="text-danger">*</span></label>
                    <select class="form-select" name="status" required>
                      <option value="">اختر الحالة</option>
                      <option value="present" ${attendance?.status === 'present' ? 'selected' : ''}>حاضر</option>
                      <option value="absent" ${attendance?.status === 'absent' ? 'selected' : ''}>غائب</option>
                      <option value="late" ${attendance?.status === 'late' ? 'selected' : ''}>متأخر</option>
                      <option value="excused" ${attendance?.status === 'excused' ? 'selected' : ''}>معذور</option>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار حالة الحضور</div>
                  </div>

                  <!-- Instructor -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">المدرب</label>
                    <select class="form-select" name="instructor">
                      <option value="">اختر المدرب</option>
                      ${this.instructors.map(instructor => `
                        <option value="${instructor._id}" ${attendance?.instructor?._id === instructor._id ? 'selected' : ''}>
                          ${instructor.fullName}
                        </option>
                      `).join('')}
                    </select>
                  </div>

                  <!-- Check-in Time (for present/late status) -->
                  <div class="col-md-6 mb-3" id="checkInTimeGroup" style="display: none;">
                    <label class="form-label">وقت الدخول</label>
                    <input type="time" class="form-control" name="checkInTime"
                           value="${attendance?.checkInTime ? new Date(attendance.checkInTime).toTimeString().slice(0,5) : ''}">
                  </div>

                  <!-- Performance Section -->
                  <div class="col-12 mt-3" id="performanceSection" style="display: none;">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-star me-2"></i>تقييم الأداء
                    </h6>
                    <div class="row">
                      <div class="col-md-4 mb-3">
                        <label class="form-label">التقنية (1-10)</label>
                        <input type="number" class="form-control" name="performance.technique"
                               min="1" max="10" value="${attendance?.performance?.technique || ''}">
                      </div>
                      <div class="col-md-4 mb-3">
                        <label class="form-label">السلوك (1-10)</label>
                        <input type="number" class="form-control" name="performance.attitude"
                               min="1" max="10" value="${attendance?.performance?.attitude || ''}">
                      </div>
                      <div class="col-md-4 mb-3">
                        <label class="form-label">الجهد (1-10)</label>
                        <input type="number" class="form-control" name="performance.effort"
                               min="1" max="10" value="${attendance?.performance?.effort || ''}">
                      </div>
                    </div>
                  </div>

                  <!-- Notes -->
                  <div class="col-12 mb-3">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-control" name="notes" rows="3" maxlength="500"
                              placeholder="أي ملاحظات إضافية...">${attendance?.notes || ''}</textarea>
                    <div class="form-text">الحد الأقصى 500 حرف</div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-primary" id="saveAttendanceBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                ${isEdit ? 'تحديث' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('attendanceModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('attendanceModal'));
    modal.show();

    // Bind form events
    this.bindAttendanceFormEvents(isEdit, attendance);
  }

  bindAttendanceFormEvents(isEdit, attendance) {
    const form = document.getElementById('attendanceForm');
    const saveBtn = document.getElementById('saveAttendanceBtn');
    const statusSelect = form.querySelector('[name="status"]');
    const checkInTimeGroup = document.getElementById('checkInTimeGroup');
    const performanceSection = document.getElementById('performanceSection');

    if (!form || !saveBtn) return;

    // Show/hide conditional fields based on status
    const toggleConditionalFields = (status) => {
      if (status === 'present' || status === 'late') {
        checkInTimeGroup.style.display = 'block';
        performanceSection.style.display = 'block';
      } else {
        checkInTimeGroup.style.display = 'none';
        performanceSection.style.display = 'none';
      }
    };

    // Initial toggle
    if (statusSelect.value) {
      toggleConditionalFields(statusSelect.value);
    }

    // Status change handler
    statusSelect.addEventListener('change', (e) => {
      toggleConditionalFields(e.target.value);
    });

    // Form validation
    form.addEventListener('input', (e) => {
      this.validateAttendanceField(e.target);
    });

    // Save button click
    saveBtn.addEventListener('click', async () => {
      if (this.validateAttendanceForm(form)) {
        await this.saveAttendance(form, isEdit, attendance);
      }
    });

    // Form submit
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      if (this.validateAttendanceForm(form)) {
        await this.saveAttendance(form, isEdit, attendance);
      }
    });
  }

  validateAttendanceField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      message = 'هذا الحقل مطلوب';
    }

    // Performance score validation
    if (field.name.includes('performance') && value) {
      const score = parseInt(value);
      if (score < 1 || score > 10) {
        isValid = false;
        message = 'التقييم يجب أن يكون بين 1 و 10';
      }
    }

    // Update field state
    if (isValid) {
      field.classList.remove('is-invalid');
      field.classList.add('is-valid');
    } else {
      field.classList.remove('is-valid');
      field.classList.add('is-invalid');
      const feedback = field.nextElementSibling;
      if (feedback && feedback.classList.contains('invalid-feedback')) {
        feedback.textContent = message;
      }
    }

    return isValid;
  }

  validateAttendanceForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!this.validateAttendanceField(field)) {
        isValid = false;
      }
    });

    return isValid;
  }

  async saveAttendance(form, isEdit, existingAttendance) {
    const saveBtn = document.getElementById('saveAttendanceBtn');
    const spinner = saveBtn.querySelector('.spinner-border');

    try {
      // Show loading
      saveBtn.disabled = true;
      spinner?.classList.remove('d-none');

      // Collect form data
      const formData = new FormData(form);
      const attendanceData = {};

      // Process form data
      for (let [key, value] of formData.entries()) {
        if (key.includes('.')) {
          // Handle nested objects (performance)
          const [parent, child] = key.split('.');
          if (!attendanceData[parent]) attendanceData[parent] = {};
          attendanceData[parent][child] = value ? parseInt(value) : null;
        } else {
          attendanceData[key] = value;
        }
      }

      // Set check-in time if present/late and not provided
      if ((attendanceData.status === 'present' || attendanceData.status === 'late') && !attendanceData.checkInTime) {
        attendanceData.checkInTime = new Date().toISOString();
      }

      // API call
      let response;
      if (isEdit) {
        response = await this.api.attendance.update(existingAttendance._id, attendanceData);
      } else {
        response = await this.api.attendance.create(attendanceData);
      }

      if (response.success) {
        this.utils.showAlert(
          isEdit ? 'تم تحديث سجل الحضور بنجاح' : 'تم تسجيل الحضور بنجاح',
          'success'
        );

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('attendanceModal'));
        modal.hide();

        // Reload data
        await this.loadAttendance();
        await this.loadTodayStats();
      } else {
        this.utils.showAlert(response.message || 'حدث خطأ أثناء الحفظ', 'error');
      }
    } catch (error) {
      console.error('Error saving attendance:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      // Hide loading
      saveBtn.disabled = false;
      spinner?.classList.add('d-none');
    }
  }

  async viewAttendance(attendanceId) {
    try {
      const response = await this.api.attendance.getById(attendanceId);
      if (response.success) {
        this.showAttendanceDetailsModal(response.data.attendance);
      } else {
        this.utils.showAlert('خطأ في تحميل سجل الحضور', 'error');
      }
    } catch (error) {
      console.error('Error loading attendance:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  }

  async editAttendance(attendanceId) {
    try {
      const response = await this.api.attendance.getById(attendanceId);
      if (response.success) {
        this.showAttendanceModal(response.data.attendance);
      } else {
        this.utils.showAlert('خطأ في تحميل سجل الحضور', 'error');
      }
    } catch (error) {
      console.error('Error loading attendance:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  }

  async deleteAttendance(attendanceId) {
    const result = await this.utils.showConfirm(
      'تأكيد الحذف',
      'هل أنت متأكد من حذف سجل الحضور هذا؟',
      'حذف',
      'إلغاء'
    );

    if (result) {
      try {
        const response = await this.api.attendance.delete(attendanceId);
        if (response.success) {
          this.utils.showAlert('تم حذف سجل الحضور بنجاح', 'success');
          await this.loadAttendance();
          await this.loadTodayStats();
        } else {
          this.utils.showAlert(response.message || 'خطأ في حذف سجل الحضور', 'error');
        }
      } catch (error) {
        console.error('Error deleting attendance:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      }
    }
  }

  async checkoutStudent(attendanceId) {
    try {
      const response = await this.api.attendance.checkout(attendanceId);
      if (response.success) {
        this.utils.showAlert('تم تسجيل خروج الطالب بنجاح', 'success');
        await this.loadAttendance();
      } else {
        this.utils.showAlert(response.message || 'خطأ في تسجيل الخروج', 'error');
      }
    } catch (error) {
      console.error('Error checking out student:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  }

  showAttendanceDetailsModal(attendance) {
    const modalHTML = `
      <div class="modal fade" id="attendanceDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">تفاصيل سجل الحضور</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                <!-- Student Info -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">معلومات الطالب</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">الاسم:</td>
                      <td>${attendance.student?.fullName || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">رقم الطالب:</td>
                      <td>${attendance.student?.studentId || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">مستوى الحزام:</td>
                      <td><span class="badge bg-warning">${attendance.student?.beltLevel || 'غير محدد'}</span></td>
                    </tr>
                  </table>
                </div>

                <!-- Session Info -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">معلومات الجلسة</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">التاريخ:</td>
                      <td>${this.utils.formatDate(attendance.date)}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">نوع الصف:</td>
                      <td><span class="badge ${this.getClassTypeColor(attendance.classType)}">${this.getClassTypeText(attendance.classType)}</span></td>
                    </tr>
                    <tr>
                      <td class="fw-medium">المدرب:</td>
                      <td>${attendance.instructor?.fullName || 'غير محدد'}</td>
                    </tr>
                  </table>
                </div>

                <!-- Attendance Info -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">معلومات الحضور</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">الحالة:</td>
                      <td><span class="badge ${this.getStatusColor(attendance.status)}">${this.getStatusText(attendance.status)}</span></td>
                    </tr>
                    <tr>
                      <td class="fw-medium">وقت الدخول:</td>
                      <td>${attendance.checkInTime ? this.utils.formatTime(attendance.checkInTime) : 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">وقت الخروج:</td>
                      <td>${attendance.checkOutTime ? this.utils.formatTime(attendance.checkOutTime) : 'لم يسجل الخروج'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">مدة الجلسة:</td>
                      <td>${attendance.sessionDuration ? `${attendance.sessionDuration} دقيقة` : 'غير محدد'}</td>
                    </tr>
                  </table>
                </div>

                <!-- Performance -->
                ${attendance.performance && (attendance.performance.technique || attendance.performance.attitude || attendance.performance.effort) ? `
                  <div class="col-md-6 mb-4">
                    <h6 class="text-primary mb-3">تقييم الأداء</h6>
                    <div class="row">
                      <div class="col-4 text-center">
                        <div class="h4 text-${this.getPerformanceColor(attendance.performance.technique || 0)}">${attendance.performance.technique || 0}</div>
                        <small class="text-muted">التقنية</small>
                      </div>
                      <div class="col-4 text-center">
                        <div class="h4 text-${this.getPerformanceColor(attendance.performance.attitude || 0)}">${attendance.performance.attitude || 0}</div>
                        <small class="text-muted">السلوك</small>
                      </div>
                      <div class="col-4 text-center">
                        <div class="h4 text-${this.getPerformanceColor(attendance.performance.effort || 0)}">${attendance.performance.effort || 0}</div>
                        <small class="text-muted">الجهد</small>
                      </div>
                    </div>
                    <div class="mt-3 text-center">
                      <div class="h5 text-${this.getPerformanceColor(attendance.overallPerformance || 0)}">
                        المعدل العام: ${attendance.overallPerformance || 0}/10
                      </div>
                    </div>
                  </div>
                ` : ''}

                <!-- Notes -->
                ${attendance.notes ? `
                  <div class="col-12 mb-3">
                    <h6 class="text-primary mb-3">ملاحظات</h6>
                    <div class="bg-light p-3 rounded">
                      ${attendance.notes}
                    </div>
                  </div>
                ` : ''}

                <!-- Metadata -->
                <div class="col-12">
                  <h6 class="text-primary mb-3">معلومات إضافية</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">سجل بواسطة:</td>
                      <td>${attendance.recordedBy?.fullName || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">تاريخ التسجيل:</td>
                      <td>${this.utils.formatDateTime(attendance.createdAt)}</td>
                    </tr>
                    ${attendance.updatedAt !== attendance.createdAt ? `
                      <tr>
                        <td class="fw-medium">آخر تحديث:</td>
                        <td>${this.utils.formatDateTime(attendance.updatedAt)}</td>
                      </tr>
                    ` : ''}
                  </table>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
              <button type="button" class="btn btn-warning" onclick="window.attendancePage.editAttendance('${attendance._id}')">
                <i class="fas fa-edit me-2"></i>تعديل
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('attendanceDetailsModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('attendanceDetailsModal'));
    modal.show();
  }

  async showBulkAttendanceModal() {
    const modalHTML = `
      <div class="modal fade" id="bulkAttendanceModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">تسجيل حضور جماعي</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="bulkAttendanceForm">
                <div class="row mb-4">
                  <div class="col-md-4">
                    <label class="form-label">التاريخ <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" name="date" value="${this.selectedDate}" required>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">نوع الصف <span class="text-danger">*</span></label>
                    <select class="form-select" name="classType" required>
                      <option value="">اختر نوع الصف</option>
                      <option value="beginner">مبتدئ</option>
                      <option value="intermediate">متوسط</option>
                      <option value="advanced">متقدم</option>
                      <option value="competition">منافسات</option>
                      <option value="private">خاص</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">المدرب</label>
                    <select class="form-select" name="instructor">
                      <option value="">اختر المدرب</option>
                      ${this.instructors.map(instructor => `
                        <option value="${instructor._id}">${instructor.fullName}</option>
                      `).join('')}
                    </select>
                  </div>
                </div>

                <div class="mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">قائمة الطلاب</h6>
                    <div>
                      <button type="button" class="btn btn-sm btn-outline-success" id="markAllPresentBtn">
                        <i class="fas fa-check-circle me-1"></i>تحديد الكل حاضر
                      </button>
                      <button type="button" class="btn btn-sm btn-outline-danger ms-2" id="markAllAbsentBtn">
                        <i class="fas fa-times-circle me-1"></i>تحديد الكل غائب
                      </button>
                    </div>
                  </div>

                  <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-sm table-hover">
                      <thead class="table-light sticky-top">
                        <tr>
                          <th>الطالب</th>
                          <th>الحالة</th>
                          <th>ملاحظات</th>
                        </tr>
                      </thead>
                      <tbody id="bulkStudentsList">
                        ${this.students.filter(s => s.status === 'active').map(student => `
                          <tr data-student-id="${student._id}">
                            <td>
                              <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
                                  <i class="fas fa-user text-muted"></i>
                                </div>
                                <div>
                                  <div class="fw-medium">${student.fullName}</div>
                                  <small class="text-muted">${student.studentId}</small>
                                </div>
                              </div>
                            </td>
                            <td>
                              <select class="form-select form-select-sm student-status" name="students[${student._id}][status]">
                                <option value="">لم يحدد</option>
                                <option value="present">حاضر</option>
                                <option value="absent">غائب</option>
                                <option value="late">متأخر</option>
                                <option value="excused">معذور</option>
                              </select>
                            </td>
                            <td>
                              <input type="text" class="form-control form-control-sm"
                                     name="students[${student._id}][notes]" placeholder="ملاحظات...">
                            </td>
                          </tr>
                        `).join('')}
                      </tbody>
                    </table>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-primary" id="saveBulkAttendanceBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                حفظ الحضور
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('bulkAttendanceModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('bulkAttendanceModal'));
    modal.show();

    // Bind bulk attendance events
    this.bindBulkAttendanceEvents();
  }

  bindBulkAttendanceEvents() {
    const markAllPresentBtn = document.getElementById('markAllPresentBtn');
    const markAllAbsentBtn = document.getElementById('markAllAbsentBtn');
    const saveBulkBtn = document.getElementById('saveBulkAttendanceBtn');

    if (markAllPresentBtn) {
      markAllPresentBtn.addEventListener('click', () => {
        document.querySelectorAll('.student-status').forEach(select => {
          select.value = 'present';
        });
      });
    }

    if (markAllAbsentBtn) {
      markAllAbsentBtn.addEventListener('click', () => {
        document.querySelectorAll('.student-status').forEach(select => {
          select.value = 'absent';
        });
      });
    }

    if (saveBulkBtn) {
      saveBulkBtn.addEventListener('click', () => {
        this.saveBulkAttendance();
      });
    }
  }

  async saveBulkAttendance() {
    const form = document.getElementById('bulkAttendanceForm');
    const saveBtn = document.getElementById('saveBulkAttendanceBtn');
    const spinner = saveBtn.querySelector('.spinner-border');

    try {
      // Show loading
      saveBtn.disabled = true;
      spinner?.classList.remove('d-none');

      const formData = new FormData(form);
      const date = formData.get('date');
      const classType = formData.get('classType');
      const instructor = formData.get('instructor');

      if (!date || !classType) {
        this.utils.showAlert('يرجى تحديد التاريخ ونوع الصف', 'error');
        return;
      }

      // Collect attendance records
      const attendanceRecords = [];
      const studentRows = document.querySelectorAll('#bulkStudentsList tr[data-student-id]');

      studentRows.forEach(row => {
        const studentId = row.dataset.studentId;
        const statusSelect = row.querySelector('.student-status');
        const notesInput = row.querySelector(`[name="students[${studentId}][notes]"]`);

        if (statusSelect.value) {
          attendanceRecords.push({
            student: studentId,
            status: statusSelect.value,
            notes: notesInput.value || ''
          });
        }
      });

      if (attendanceRecords.length === 0) {
        this.utils.showAlert('يرجى تحديد حالة الحضور لطالب واحد على الأقل', 'error');
        return;
      }

      const response = await this.api.attendance.createBulk({
        attendanceRecords,
        classType,
        instructor: instructor || null,
        date
      });

      if (response.success) {
        this.utils.showAlert(`تم تسجيل حضور ${attendanceRecords.length} طالب بنجاح`, 'success');

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('bulkAttendanceModal'));
        modal.hide();

        // Reload data
        await this.loadAttendance();
        await this.loadTodayStats();
      } else {
        this.utils.showAlert(response.message || 'حدث خطأ أثناء تسجيل الحضور', 'error');
      }
    } catch (error) {
      console.error('Error saving bulk attendance:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      // Hide loading
      saveBtn.disabled = false;
      spinner?.classList.add('d-none');
    }
  }

  async exportAttendance() {
    try {
      const params = {
        search: this.searchQuery,
        startDate: this.selectedDate,
        endDate: this.selectedDate,
        ...this.filters
      };

      // Get all attendance for export
      const response = await this.api.attendance.getAll({ ...params, limit: 1000 });

      if (!response.success) {
        this.utils.showAlert('خطأ في تصدير البيانات', 'error');
        return;
      }

      const attendance = response.data.attendance;

      // Prepare CSV data
      const csvData = [
        ['الطالب', 'رقم الطالب', 'التاريخ', 'نوع الصف', 'الحالة', 'وقت الدخول', 'وقت الخروج', 'المدرب', 'التقييم العام', 'ملاحظات']
      ];

      attendance.forEach(record => {
        csvData.push([
          record.student?.fullName || '',
          record.student?.studentId || '',
          this.utils.formatDate(record.date),
          this.getClassTypeText(record.classType),
          this.getStatusText(record.status),
          record.checkInTime ? this.utils.formatTime(record.checkInTime) : '',
          record.checkOutTime ? this.utils.formatTime(record.checkOutTime) : '',
          record.instructor?.fullName || '',
          record.overallPerformance || '',
          record.notes || ''
        ]);
      });

      // Convert to CSV and download
      const csv = csvData.map(row => row.join(',')).join('\n');
      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `attendance_${this.selectedDate}.csv`);
      link.style.visibility = 'hidden';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.utils.showAlert('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
      console.error('Error exporting attendance:', error);
      this.utils.showAlert('خطأ في تصدير البيانات', 'error');
    }
  }

  async showAttendanceReport() {
    // This would open a detailed attendance report modal
    // For now, we'll show a simple alert
    this.utils.showAlert('تقرير الحضور قيد التطوير', 'info');
  }
}

// Export for global access
window.AttendancePage = AttendancePage;