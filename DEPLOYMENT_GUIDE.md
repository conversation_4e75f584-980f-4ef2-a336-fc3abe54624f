# 🚀 Taekwondo CRM Deployment Guide

## Quick Start (Development)

### Prerequisites
- Node.js v14+ 
- MongoDB v4.4+
- Git

### 1. <PERSON><PERSON> and Setup
```bash
git clone <repository-url>
cd taekwondo-crm-complete

# Backend setup
cd backend
npm install
cp .env.example .env
# Edit .env with your configuration

# Initialize database with sample data
npm run init-users

# Start backend server
npm start
```

### 2. Frontend Setup
```bash
# Open frontend in browser
cd ../frontend
# Use any local server:
python -m http.server 3000
# OR
php -S localhost:3000
# OR open index.html directly in browser
```

### 3. Access the Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Health Check: http://localhost:5000/health

### 4. Default Login Credentials
- **Admin**: username: `admin`, password: `admin123`
- **Coach**: username: `coach1`, password: `coach123`
- **Receptionist**: username: `receptionist`, password: `reception123`

## Production Deployment

### Environment Variables (.env)
```env
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://localhost:27017/taekwondo_crm
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-secret-key-here
FRONTEND_URL=https://yourdomain.com
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_TO_CONSOLE=false
```

### Docker Deployment

1. **Create Dockerfile (backend)**
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]
```

2. **Create docker-compose.yml**
```yaml
version: '3.8'
services:
  mongodb:
    image: mongo:4.4
    restart: always
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_DATABASE: taekwondo_crm

  backend:
    build: ./backend
    restart: always
    ports:
      - "5000:5000"
    environment:
      MONGODB_URI: mongodb://mongodb:27017/taekwondo_crm
      NODE_ENV: production
    depends_on:
      - mongodb

  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./frontend:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend

volumes:
  mongodb_data:
```

3. **Deploy**
```bash
docker-compose up -d
```

### Traditional Server Deployment

1. **Install Dependencies**
```bash
# Install Node.js, MongoDB, Nginx
sudo apt update
sudo apt install nodejs npm mongodb nginx
```

2. **Setup Application**
```bash
# Clone repository
git clone <repository-url>
cd taekwondo-crm-complete/backend

# Install dependencies
npm ci --only=production

# Setup environment
cp .env.example .env
# Edit .env for production

# Initialize database
npm run init-users

# Setup PM2 for process management
npm install -g pm2
pm2 start index.js --name taekwondo-crm
pm2 startup
pm2 save
```

3. **Configure Nginx**
```nginx
server {
    listen 80;
    server_name yourdomain.com;

    # Frontend
    location / {
        root /path/to/taekwondo-crm-complete/frontend;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Health check
    location /health {
        proxy_pass http://localhost:5000;
    }
}
```

4. **SSL Certificate (Let's Encrypt)**
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

## Database Backup & Restore

### Backup
```bash
# Create backup
mongodump --db taekwondo_crm --out /backup/$(date +%Y%m%d)

# Automated daily backup
echo "0 2 * * * mongodump --db taekwondo_crm --out /backup/\$(date +\%Y\%m\%d)" | crontab -
```

### Restore
```bash
# Restore from backup
mongorestore --db taekwondo_crm /backup/20240101/taekwondo_crm/
```

## Monitoring & Maintenance

### Log Management
```bash
# View application logs
pm2 logs taekwondo-crm

# View MongoDB logs
sudo tail -f /var/log/mongodb/mongod.log

# Application logs location
tail -f backend/logs/$(date +%Y-%m-%d).log
```

### Performance Monitoring
```bash
# Monitor system resources
htop

# Monitor MongoDB performance
mongostat

# Monitor application performance
pm2 monit
```

### Security Checklist
- [ ] Change default passwords
- [ ] Use strong JWT secrets
- [ ] Enable HTTPS/SSL
- [ ] Configure firewall
- [ ] Regular security updates
- [ ] Monitor logs for suspicious activity
- [ ] Backup database regularly
- [ ] Use environment variables for secrets

## Troubleshooting

### Common Issues

1. **Port 5000 already in use**
```bash
# Find process using port
lsof -i :5000
# Kill process
kill -9 <PID>
```

2. **MongoDB connection failed**
```bash
# Check MongoDB status
sudo systemctl status mongodb
# Start MongoDB
sudo systemctl start mongodb
```

3. **Permission denied errors**
```bash
# Fix file permissions
chmod +x backend/index.js
chown -R $USER:$USER taekwondo-crm-complete/
```

4. **Frontend not loading**
- Check CORS configuration
- Verify API URL in axiosConfig.js
- Check browser console for errors

### Performance Optimization

1. **Database Indexing**
```javascript
// Add indexes for frequently queried fields
db.students.createIndex({ "studentId": 1 })
db.students.createIndex({ "status": 1 })
db.attendance.createIndex({ "student": 1, "date": -1 })
```

2. **Nginx Caching**
```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

3. **Application Optimization**
- Enable gzip compression
- Use CDN for static assets
- Implement Redis caching
- Optimize database queries

## Support

For technical support:
- Check logs first
- Review API documentation
- Run test suite: `npm test`
- Check GitHub issues
- Contact support team

---

**Deployment completed successfully! 🎉**
