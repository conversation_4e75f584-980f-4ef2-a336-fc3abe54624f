{"name": "taekwondo-crm-backend", "version": "1.0.0", "description": "Backend for Taekwondo CRM system", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "init-users": "node initUsers.js", "test": "node test/api.test.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "validator": "^13.15.15", "xss": "^1.0.15"}, "devDependencies": {"axios": "^1.10.0", "nodemon": "^3.0.1"}, "keywords": ["taekwondo", "crm", "nodejs", "express", "mongodb"], "author": "", "license": "ISC"}