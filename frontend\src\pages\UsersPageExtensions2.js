// UsersPage Extensions 2 - Additional CRUD operations and utility methods
// This file extends the UsersPage class with more functionality

// Extend UsersPage prototype with additional methods
Object.assign(UsersPage.prototype, {
  
  async saveUser(form, isEdit, existingUser) {
    const saveBtn = document.getElementById('saveUserBtn');
    const spinner = saveBtn.querySelector('.spinner-border');
    
    try {
      // Show loading
      saveBtn.disabled = true;
      spinner?.classList.remove('d-none');

      // Collect form data
      const formData = new FormData(form);
      const userData = {
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        role: formData.get('role'),
        isActive: formData.get('isActive') === 'true'
      };

      // Add username and password for new users
      if (!isEdit) {
        userData.username = formData.get('username');
        userData.password = formData.get('password');
      }

      // Collect permissions
      const permissions = [];
      const permissionCheckboxes = form.querySelectorAll('input[name="permissions"]:checked');
      permissionCheckboxes.forEach(checkbox => {
        permissions.push(checkbox.value);
      });
      userData.permissions = permissions;

      // API call
      let response;
      if (isEdit) {
        response = await this.api.users.update(existingUser._id, userData);
      } else {
        response = await this.api.users.create(userData);
      }

      if (response.success) {
        this.utils.showAlert(
          isEdit ? 'تم تحديث المستخدم بنجاح' : 'تم إنشاء المستخدم بنجاح',
          'success'
        );
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
        modal.hide();
        
        // Reload data
        await this.loadUsers();
        await this.loadStatistics();
      } else {
        this.utils.showAlert(response.message || 'حدث خطأ أثناء الحفظ', 'error');
      }
    } catch (error) {
      console.error('Error saving user:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      // Hide loading
      saveBtn.disabled = false;
      spinner?.classList.add('d-none');
    }
  },

  async viewUser(userId) {
    try {
      const response = await this.api.users.getById(userId);
      if (response.success) {
        this.showUserDetailsModal(response.data.user);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات المستخدم', 'error');
      }
    } catch (error) {
      console.error('Error loading user:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  async editUser(userId) {
    try {
      const response = await this.api.users.getById(userId);
      if (response.success) {
        this.showUserModal(response.data.user);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات المستخدم', 'error');
      }
    } catch (error) {
      console.error('Error loading user:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  async resetPassword(userId) {
    const result = await this.utils.showConfirm(
      'إعادة تعيين كلمة المرور',
      'هل أنت متأكد من إعادة تعيين كلمة المرور لهذا المستخدم؟',
      'إعادة تعيين',
      'إلغاء'
    );

    if (result) {
      // Show password input modal
      this.showPasswordResetModal(userId);
    }
  },

  showPasswordResetModal(userId) {
    const modalHTML = `
      <div class="modal fade" id="passwordResetModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="passwordResetForm">
                <div class="mb-3">
                  <label class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                  <input type="password" class="form-control" name="newPassword" required minlength="6"
                         placeholder="كلمة المرور الجديدة (6 أحرف على الأقل)">
                  <div class="invalid-feedback">كلمة المرور مطلوبة (6 أحرف على الأقل)</div>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                  <input type="password" class="form-control" name="confirmNewPassword" required minlength="6"
                         placeholder="تأكيد كلمة المرور الجديدة">
                  <div class="invalid-feedback">تأكيد كلمة المرور مطلوب</div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-warning" id="confirmPasswordResetBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                إعادة تعيين
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('passwordResetModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('passwordResetModal'));
    modal.show();

    // Bind events
    this.bindPasswordResetEvents(userId);
  },

  bindPasswordResetEvents(userId) {
    const form = document.getElementById('passwordResetForm');
    const confirmBtn = document.getElementById('confirmPasswordResetBtn');
    const newPasswordInput = form.querySelector('[name="newPassword"]');
    const confirmPasswordInput = form.querySelector('[name="confirmNewPassword"]');

    // Password confirmation validation
    confirmPasswordInput.addEventListener('input', () => {
      if (newPasswordInput.value !== confirmPasswordInput.value) {
        confirmPasswordInput.setCustomValidity('كلمات المرور غير متطابقة');
        confirmPasswordInput.classList.add('is-invalid');
      } else {
        confirmPasswordInput.setCustomValidity('');
        confirmPasswordInput.classList.remove('is-invalid');
        confirmPasswordInput.classList.add('is-valid');
      }
    });

    // Confirm reset
    confirmBtn.addEventListener('click', async () => {
      const newPassword = newPasswordInput.value;
      const confirmPassword = confirmPasswordInput.value;

      if (!newPassword || newPassword.length < 6) {
        this.utils.showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
      }

      if (newPassword !== confirmPassword) {
        this.utils.showAlert('كلمات المرور غير متطابقة', 'error');
        return;
      }

      try {
        confirmBtn.disabled = true;
        confirmBtn.querySelector('.spinner-border')?.classList.remove('d-none');

        const response = await this.api.users.resetPassword(userId, { newPassword });
        
        if (response.success) {
          this.utils.showAlert('تم إعادة تعيين كلمة المرور بنجاح', 'success');
          
          // Close modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('passwordResetModal'));
          modal.hide();
        } else {
          this.utils.showAlert(response.message || 'خطأ في إعادة تعيين كلمة المرور', 'error');
        }
      } catch (error) {
        console.error('Error resetting password:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      } finally {
        confirmBtn.disabled = false;
        confirmBtn.querySelector('.spinner-border')?.classList.add('d-none');
      }
    });
  },

  async toggleUserStatus(userId) {
    try {
      const user = this.users.find(u => u._id === userId);
      if (!user) return;

      const action = user.isActive ? 'إيقاف' : 'تفعيل';
      const result = await this.utils.showConfirm(
        `${action} المستخدم`,
        `هل أنت متأكد من ${action} هذا المستخدم؟`,
        action,
        'إلغاء'
      );

      if (result) {
        const response = await this.api.users.update(userId, { isActive: !user.isActive });
        if (response.success) {
          this.utils.showAlert(`تم ${action} المستخدم بنجاح`, 'success');
          await this.loadUsers();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || `خطأ في ${action} المستخدم`, 'error');
        }
      }
    } catch (error) {
      console.error('Error toggling user status:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  async deactivateUser(userId) {
    const result = await this.utils.showConfirm(
      'إلغاء تفعيل المستخدم',
      'هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.',
      'إلغاء التفعيل',
      'إلغاء'
    );

    if (result) {
      try {
        const response = await this.api.users.deactivate(userId);
        if (response.success) {
          this.utils.showAlert('تم إلغاء تفعيل المستخدم بنجاح', 'success');
          await this.loadUsers();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || 'خطأ في إلغاء تفعيل المستخدم', 'error');
        }
      } catch (error) {
        console.error('Error deactivating user:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      }
    }
  },

  async exportUsers() {
    try {
      const params = {
        search: this.searchQuery,
        ...this.filters
      };

      // Get all users for export
      const response = await this.api.users.getAll({ ...params, limit: 1000 });
      
      if (!response.success) {
        this.utils.showAlert('خطأ في تصدير البيانات', 'error');
        return;
      }

      const users = response.data.users;
      
      // Prepare CSV data
      const csvData = [
        ['الاسم الكامل', 'اسم المستخدم', 'البريد الإلكتروني', 'الهاتف', 'الدور', 'الحالة', 'آخر دخول', 'تاريخ الإنشاء']
      ];

      users.forEach(user => {
        csvData.push([
          user.fullName,
          user.username,
          user.email,
          user.phone || '',
          this.roles[user.role] || user.role,
          user.isActive ? 'نشط' : 'غير نشط',
          user.lastLogin ? this.utils.formatDateTime(user.lastLogin) : 'لم يسجل دخول',
          this.utils.formatDateTime(user.createdAt)
        ]);
      });

      // Convert to CSV and download
      const csv = csvData.map(row => row.join(',')).join('\n');
      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      
      link.setAttribute('href', url);
      link.setAttribute('download', `users_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      this.utils.showAlert('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
      console.error('Error exporting users:', error);
      this.utils.showAlert('خطأ في تصدير البيانات', 'error');
    }
  },

  showUserDetailsModal(user) {
    const modalHTML = `
      <div class="modal fade" id="userDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">تفاصيل المستخدم</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                <!-- User Header -->
                <div class="col-12 mb-4">
                  <div class="text-center">
                    <div class="avatar-xl bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                      ${user.profileImage ?
                        `<img src="${user.profileImage}" class="rounded-circle" width="80" height="80" alt="${user.fullName}">` :
                        `<i class="fas fa-user fa-3x text-muted"></i>`
                      }
                    </div>
                    <h4>${user.fullName}</h4>
                    <p class="text-muted">@${user.username}</p>
                    <div class="d-flex justify-content-center gap-2 mt-2">
                      <span class="badge ${this.getRoleColor(user.role)}">${this.roles[user.role] || user.role}</span>
                      <span class="badge ${user.isActive ? 'bg-success' : 'bg-secondary'}">${user.isActive ? 'نشط' : 'غير نشط'}</span>
                    </div>
                  </div>
                </div>

                <!-- Basic Info -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">المعلومات الأساسية</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">الاسم الكامل:</td>
                      <td>${user.fullName}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">اسم المستخدم:</td>
                      <td>@${user.username}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">البريد الإلكتروني:</td>
                      <td>${user.email}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">رقم الهاتف:</td>
                      <td>${user.phone || 'غير محدد'}</td>
                    </tr>
                  </table>
                </div>

                <!-- Role & Status -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">الدور والحالة</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">الدور:</td>
                      <td><span class="badge ${this.getRoleColor(user.role)}">${this.roles[user.role] || user.role}</span></td>
                    </tr>
                    <tr>
                      <td class="fw-medium">الحالة:</td>
                      <td><span class="badge ${user.isActive ? 'bg-success' : 'bg-secondary'}">${user.isActive ? 'نشط' : 'غير نشط'}</span></td>
                    </tr>
                    <tr>
                      <td class="fw-medium">آخر دخول:</td>
                      <td>${user.lastLogin ? this.utils.formatDateTime(user.lastLogin) : 'لم يسجل دخول'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">تاريخ الإنشاء:</td>
                      <td>${this.utils.formatDateTime(user.createdAt)}</td>
                    </tr>
                  </table>
                </div>

                <!-- Permissions -->
                <div class="col-12 mb-4">
                  <h6 class="text-primary mb-3">الصلاحيات</h6>
                  ${user.role === 'admin' ?
                    '<div class="alert alert-warning"><i class="fas fa-crown me-2"></i>مدير النظام - لديه جميع الصلاحيات</div>' :
                    user.permissions?.length > 0 ?
                      `<div class="row">
                        ${user.permissions.map(perm => `
                          <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                              <i class="fas fa-check text-success me-2"></i>
                              <span>${this.permissions[perm] || perm}</span>
                            </div>
                          </div>
                        `).join('')}
                      </div>` :
                      '<div class="text-muted">لا توجد صلاحيات محددة</div>'
                  }
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
              <button type="button" class="btn btn-info" onclick="window.usersPage.resetPassword('${user._id}')">
                <i class="fas fa-key me-2"></i>إعادة تعيين كلمة المرور
              </button>
              <button type="button" class="btn btn-warning" onclick="window.usersPage.editUser('${user._id}')">
                <i class="fas fa-edit me-2"></i>تعديل
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('userDetailsModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
    modal.show();
  },

  showRolesPermissionsModal() {
    const modalHTML = `
      <div class="modal fade" id="rolesPermissionsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">الأدوار والصلاحيات</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                <div class="col-md-4 mb-4">
                  <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                      <h6 class="mb-0"><i class="fas fa-user-shield me-2"></i>مدير النظام</h6>
                    </div>
                    <div class="card-body">
                      <p class="small text-muted">لديه جميع الصلاحيات في النظام</p>
                      <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>جميع الصلاحيات</li>
                        <li><i class="fas fa-check text-success me-2"></i>إدارة المستخدمين</li>
                        <li><i class="fas fa-check text-success me-2"></i>الوصول الكامل</li>
                      </ul>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-4 mb-4">
                  <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                      <h6 class="mb-0"><i class="fas fa-user-tie me-2"></i>مدرب</h6>
                    </div>
                    <div class="card-body">
                      <p class="small text-muted">صلاحيات التدريب والحضور</p>
                      <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>عرض الطلاب</li>
                        <li><i class="fas fa-check text-success me-2"></i>تسجيل الحضور</li>
                        <li><i class="fas fa-check text-success me-2"></i>عرض التقارير</li>
                      </ul>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-4 mb-4">
                  <div class="card border-info">
                    <div class="card-header bg-info text-white">
                      <h6 class="mb-0"><i class="fas fa-user me-2"></i>موظف استقبال</h6>
                    </div>
                    <div class="card-body">
                      <p class="small text-muted">صلاحيات الاستقبال والإدارة</p>
                      <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>إدارة الطلاب</li>
                        <li><i class="fas fa-check text-success me-2"></i>إدارة الاشتراكات</li>
                        <li><i class="fas fa-check text-success me-2"></i>عرض التقارير</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              
              <h6 class="text-primary mb-3">تفاصيل الصلاحيات</h6>
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>الصلاحية</th>
                      <th>الوصف</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${Object.entries(this.permissions).map(([key, label]) => `
                      <tr>
                        <td><code>${key}</code></td>
                        <td>${label}</td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('rolesPermissionsModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('rolesPermissionsModal'));
    modal.show();
  }
});
