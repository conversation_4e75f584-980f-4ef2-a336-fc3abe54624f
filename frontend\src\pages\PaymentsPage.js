// Payments Management Page
class PaymentsPage {
  constructor() {
    this.state = window.stateManager;
    this.api = window.apiServices;
    this.utils = window.utils;
    this.currentPage = 1;
    this.pageSize = 10;
    this.searchQuery = '';
    this.filters = {};
    this.sortBy = 'paymentDate';
    this.sortOrder = 'desc';
    this.payments = [];
    this.totalPayments = 0;
    this.subscriptions = [];
    this.isLoading = false;
  }

  async render(container) {
    container.innerHTML = `
      <div class="payments-page">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h3 class="mb-1">إدارة المدفوعات</h3>
            <p class="text-muted mb-0">تتبع وإدارة المدفوعات والإيرادات</p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" id="financialReportBtn">
              <i class="fas fa-chart-line me-2"></i>التقرير المالي
            </button>
            <button class="btn btn-outline-secondary" id="exportPaymentsBtn">
              <i class="fas fa-download me-2"></i>تصدير البيانات
            </button>
            <button class="btn btn-primary" id="addPaymentBtn">
              <i class="fas fa-plus me-2"></i>تسجيل دفعة
            </button>
          </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-success bg-gradient rounded-circle p-3">
                      <i class="fas fa-money-bill-wave text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">إجمالي الإيرادات</div>
                    <div class="h4 mb-0" id="totalRevenueAmount">0 ريال</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-primary bg-gradient rounded-circle p-3">
                      <i class="fas fa-receipt text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">عدد المعاملات</div>
                    <div class="h4 mb-0" id="totalTransactionsCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-warning bg-gradient rounded-circle p-3">
                      <i class="fas fa-clock text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">مدفوعات معلقة</div>
                    <div class="h4 mb-0" id="pendingPaymentsCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-info bg-gradient rounded-circle p-3">
                      <i class="fas fa-undo text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">مبلغ الاستردادات</div>
                    <div class="h4 mb-0" id="totalRefundsAmount">0 ريال</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Search and Filters -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-3">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input type="text" class="form-control" id="searchInput" 
                         placeholder="البحث برقم الدفعة أو الطالب...">
                </div>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                  <option value="">جميع الحالات</option>
                  <option value="completed">مكتمل</option>
                  <option value="pending">معلق</option>
                  <option value="failed">فاشل</option>
                  <option value="cancelled">ملغي</option>
                  <option value="refunded">مسترد</option>
                </select>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="paymentMethodFilter">
                  <option value="">طريقة الدفع</option>
                  <option value="cash">نقدي</option>
                  <option value="card">بطاقة</option>
                  <option value="bank_transfer">تحويل بنكي</option>
                  <option value="online">دفع إلكتروني</option>
                  <option value="check">شيك</option>
                </select>
              </div>
              <div class="col-md-2">
                <input type="date" class="form-control" id="startDateFilter" placeholder="من تاريخ">
              </div>
              <div class="col-md-2">
                <input type="date" class="form-control" id="endDateFilter" placeholder="إلى تاريخ">
              </div>
              <div class="col-md-1">
                <button class="btn btn-outline-secondary w-100" id="clearFiltersBtn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Payments Table -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة المدفوعات</h5>
            <div class="d-flex align-items-center gap-3">
              <div class="d-flex align-items-center">
                <label class="form-label mb-0 me-2">عرض:</label>
                <select class="form-select form-select-sm" id="pageSizeSelect" style="width: auto;">
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>رقم الدفعة</th>
                    <th>الطالب</th>
                    <th>الاشتراك</th>
                    <th>المبلغ</th>
                    <th>طريقة الدفع</th>
                    <th>تاريخ الدفع</th>
                    <th>رقم الإيصال</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody id="paymentsTableBody">
                  <!-- Payments will be loaded here -->
                </tbody>
              </table>
            </div>
            
            <!-- Loading State -->
            <div id="loadingState" class="text-center py-5 d-none">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
              </div>
              <p class="mt-3 text-muted">جاري تحميل المدفوعات...</p>
            </div>
            
            <!-- Empty State -->
            <div id="emptyState" class="text-center py-5 d-none">
              <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا توجد مدفوعات</h5>
              <p class="text-muted">لم يتم العثور على مدفوعات مطابقة للبحث أو الفلاتر المحددة</p>
              <button class="btn btn-primary" id="addFirstPaymentBtn">
                <i class="fas fa-plus me-2"></i>تسجيل أول دفعة
              </button>
            </div>
          </div>
          
          <!-- Pagination -->
          <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div class="text-muted">
                عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span> 
                من أصل <span id="totalCount">0</span> دفعة
              </div>
              <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                  <!-- Pagination will be generated here -->
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    `;

    await this.bindEvents();
    await this.loadInitialData();
    await this.loadPayments();
    await this.loadStatistics();
  }

  async bindEvents() {
    // Search input with debounce
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.searchQuery = e.target.value;
          this.currentPage = 1;
          this.loadPayments();
        }, 300);
      });
    }

    // Filter changes
    ['statusFilter', 'paymentMethodFilter'].forEach(filterId => {
      const filter = document.getElementById(filterId);
      if (filter) {
        filter.addEventListener('change', (e) => {
          const filterKey = filterId.replace('Filter', '');
          if (e.target.value) {
            this.filters[filterKey] = e.target.value;
          } else {
            delete this.filters[filterKey];
          }
          this.currentPage = 1;
          this.loadPayments();
        });
      }
    });

    // Date filters
    const startDateFilter = document.getElementById('startDateFilter');
    const endDateFilter = document.getElementById('endDateFilter');

    if (startDateFilter) {
      startDateFilter.addEventListener('change', (e) => {
        if (e.target.value) {
          this.filters.startDate = e.target.value;
        } else {
          delete this.filters.startDate;
        }
        this.currentPage = 1;
        this.loadPayments();
      });
    }

    if (endDateFilter) {
      endDateFilter.addEventListener('change', (e) => {
        if (e.target.value) {
          this.filters.endDate = e.target.value;
        } else {
          delete this.filters.endDate;
        }
        this.currentPage = 1;
        this.loadPayments();
      });
    }

    // Clear filters
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', () => {
        this.clearFilters();
      });
    }

    // Page size change
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
      pageSizeSelect.addEventListener('change', (e) => {
        this.pageSize = parseInt(e.target.value);
        this.currentPage = 1;
        this.loadPayments();
      });
    }

    // Action buttons
    const addPaymentBtn = document.getElementById('addPaymentBtn');
    const addFirstPaymentBtn = document.getElementById('addFirstPaymentBtn');
    const financialReportBtn = document.getElementById('financialReportBtn');
    const exportPaymentsBtn = document.getElementById('exportPaymentsBtn');

    if (addPaymentBtn) {
      addPaymentBtn.addEventListener('click', () => this.showPaymentModal());
    }
    if (addFirstPaymentBtn) {
      addFirstPaymentBtn.addEventListener('click', () => this.showPaymentModal());
    }
    if (financialReportBtn) {
      financialReportBtn.addEventListener('click', () => this.showFinancialReport());
    }
    if (exportPaymentsBtn) {
      exportPaymentsBtn.addEventListener('click', () => this.exportPayments());
    }

    // Table row actions (using event delegation)
    const tableBody = document.getElementById('paymentsTableBody');
    if (tableBody) {
      tableBody.addEventListener('click', (e) => {
        const paymentId = e.target.closest('tr')?.dataset.paymentId;
        if (!paymentId) return;

        if (e.target.closest('.btn-view')) {
          this.viewPayment(paymentId);
        } else if (e.target.closest('.btn-receipt')) {
          this.printReceipt(paymentId);
        } else if (e.target.closest('.btn-refund')) {
          this.processRefund(paymentId);
        } else if (e.target.closest('.btn-delete')) {
          this.deletePayment(paymentId);
        }
      });
    }

    // Pagination (using event delegation)
    const pagination = document.getElementById('pagination');
    if (pagination) {
      pagination.addEventListener('click', (e) => {
        e.preventDefault();
        const page = e.target.dataset.page;
        if (page && page !== this.currentPage.toString()) {
          this.currentPage = parseInt(page);
          this.loadPayments();
        }
      });
    }
  }

  clearFilters() {
    this.searchQuery = '';
    this.filters = {};
    this.currentPage = 1;

    // Reset form elements
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('paymentMethodFilter').value = '';
    document.getElementById('startDateFilter').value = '';
    document.getElementById('endDateFilter').value = '';

    this.loadPayments();
  }

  async loadInitialData() {
    try {
      // Load subscriptions for payment creation
      const subscriptionsResponse = await this.api.subscriptions.getAll({
        limit: 1000,
        paymentStatus: 'unpaid,partial'
      });

      if (subscriptionsResponse.success) {
        this.subscriptions = subscriptionsResponse.data.subscriptions;
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  }

  async loadPayments() {
    try {
      this.setLoading(true);

      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        sortBy: this.sortBy,
        sortOrder: this.sortOrder
      };

      if (this.searchQuery) {
        params.search = this.searchQuery;
      }

      Object.assign(params, this.filters);

      const response = await this.api.payments.getAll(params);

      if (response.success) {
        this.payments = response.data.payments;
        this.totalPayments = response.data.total;
        this.renderPaymentsTable();
        this.renderPagination();
        this.updatePaginationInfo();
      } else {
        this.utils.showAlert('خطأ في تحميل المدفوعات', 'error');
      }
    } catch (error) {
      console.error('Error loading payments:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  async loadStatistics() {
    try {
      const response = await this.api.payments.getStats();
      if (response.success) {
        const stats = response.data;
        document.getElementById('totalRevenueAmount').textContent = `${stats.totalRevenue || 0} ريال`;
        document.getElementById('totalTransactionsCount').textContent = stats.totalTransactions || 0;
        document.getElementById('pendingPaymentsCount').textContent = stats.pendingPayments || 0;
        document.getElementById('totalRefundsAmount').textContent = `${stats.totalRefunds || 0} ريال`;
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    const loadingState = document.getElementById('loadingState');
    const tableBody = document.getElementById('paymentsTableBody');

    if (loading) {
      loadingState?.classList.remove('d-none');
      tableBody?.classList.add('d-none');
    } else {
      loadingState?.classList.add('d-none');
      tableBody?.classList.remove('d-none');
    }
  }

  renderPaymentsTable() {
    const tableBody = document.getElementById('paymentsTableBody');
    const emptyState = document.getElementById('emptyState');

    if (!tableBody) return;

    if (this.payments.length === 0) {
      tableBody.classList.add('d-none');
      emptyState?.classList.remove('d-none');
      return;
    }

    emptyState?.classList.add('d-none');
    tableBody.classList.remove('d-none');

    tableBody.innerHTML = this.payments.map(payment => `
      <tr data-payment-id="${payment._id}">
        <td>
          <span class="fw-medium">${payment.paymentId}</span>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
              <i class="fas fa-user text-muted"></i>
            </div>
            <div>
              <div class="fw-medium">${payment.student?.fullName || 'غير محدد'}</div>
              <small class="text-muted">${payment.student?.studentId || ''}</small>
            </div>
          </div>
        </td>
        <td>
          <div class="fw-medium">${payment.subscription?.subscriptionId || 'غير محدد'}</div>
          <small class="text-muted">${payment.subscription?.plan?.name ? this.getPlanText(payment.subscription.plan.name) : ''}</small>
        </td>
        <td>
          <div class="fw-medium">${payment.amount} ريال</div>
          ${payment.refund?.amount > 0 ? `
            <small class="text-danger">
              مسترد: ${payment.refund.amount} ريال
            </small>
          ` : ''}
          ${payment.netAmount !== payment.amount ? `
            <div class="small text-success">صافي: ${payment.netAmount} ريال</div>
          ` : ''}
        </td>
        <td>
          <span class="badge ${this.getPaymentMethodColor(payment.paymentMethod)}">
            ${this.getPaymentMethodText(payment.paymentMethod)}
          </span>
        </td>
        <td>
          <div class="fw-medium">${this.utils.formatDate(payment.paymentDate)}</div>
          <small class="text-muted">${this.utils.formatTime(payment.paymentDate)}</small>
        </td>
        <td>
          ${payment.receiptNumber ? `
            <span class="fw-medium">${payment.receiptNumber}</span>
          ` : '<span class="text-muted">-</span>'}
        </td>
        <td>
          <span class="badge ${this.getStatusColor(payment.status)}">
            ${this.getStatusText(payment.status)}
          </span>
        </td>
        <td>
          <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary btn-view" title="عرض التفاصيل">
              <i class="fas fa-eye"></i>
            </button>
            ${payment.receiptNumber ? `
              <button class="btn btn-outline-info btn-receipt" title="طباعة الإيصال">
                <i class="fas fa-print"></i>
              </button>
            ` : ''}
            ${payment.status === 'completed' && !payment.refund?.amount ? `
              <button class="btn btn-outline-warning btn-refund" title="استرداد">
                <i class="fas fa-undo"></i>
              </button>
            ` : ''}
            ${payment.status === 'pending' ? `
              <button class="btn btn-outline-danger btn-delete" title="حذف">
                <i class="fas fa-trash"></i>
              </button>
            ` : ''}
          </div>
        </td>
      </tr>
    `).join('');
  }

  getPlanText(planName) {
    const texts = {
      'monthly': 'شهري',
      'quarterly': 'ربع سنوي',
      'semi-annual': 'نصف سنوي',
      'annual': 'سنوي',
      'unlimited': 'غير محدود'
    };
    return texts[planName] || planName;
  }

  getPaymentMethodColor(method) {
    const colors = {
      'cash': 'bg-success',
      'card': 'bg-primary',
      'bank_transfer': 'bg-info',
      'online': 'bg-warning',
      'check': 'bg-secondary'
    };
    return colors[method] || 'bg-secondary';
  }

  getPaymentMethodText(method) {
    const texts = {
      'cash': 'نقدي',
      'card': 'بطاقة',
      'bank_transfer': 'تحويل بنكي',
      'online': 'دفع إلكتروني',
      'check': 'شيك'
    };
    return texts[method] || method;
  }

  getStatusColor(status) {
    const colors = {
      'completed': 'bg-success',
      'pending': 'bg-warning',
      'failed': 'bg-danger',
      'cancelled': 'bg-secondary',
      'refunded': 'bg-info'
    };
    return colors[status] || 'bg-secondary';
  }

  getStatusText(status) {
    const texts = {
      'completed': 'مكتمل',
      'pending': 'معلق',
      'failed': 'فاشل',
      'cancelled': 'ملغي',
      'refunded': 'مسترد'
    };
    return texts[status] || status;
  }

  renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    const totalPages = Math.ceil(this.totalPayments / this.pageSize);

    if (totalPages <= 1) {
      pagination.innerHTML = '';
      return;
    }

    let paginationHTML = '';

    // Previous button
    paginationHTML += `
      <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage - 1}">
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
    `;

    // Page numbers
    const startPage = Math.max(1, this.currentPage - 2);
    const endPage = Math.min(totalPages, this.currentPage + 2);

    if (startPage > 1) {
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="1">1</a>
        </li>
      `;
      if (startPage > 2) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
        <li class="page-item ${i === this.currentPage ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
        </li>
      `;
    }

    // Next button
    paginationHTML += `
      <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage + 1}">
          <i class="fas fa-chevron-left"></i>
        </a>
      </li>
    `;

    pagination.innerHTML = paginationHTML;
  }

  updatePaginationInfo() {
    const showingFrom = document.getElementById('showingFrom');
    const showingTo = document.getElementById('showingTo');
    const totalCount = document.getElementById('totalCount');

    if (showingFrom && showingTo && totalCount) {
      const from = (this.currentPage - 1) * this.pageSize + 1;
      const to = Math.min(this.currentPage * this.pageSize, this.totalPayments);

      showingFrom.textContent = this.totalPayments > 0 ? from : 0;
      showingTo.textContent = to;
      totalCount.textContent = this.totalPayments;
    }
  }

  // CRUD Operations
  async showPaymentModal(payment = null) {
    const isEdit = !!payment;
    const modalTitle = isEdit ? 'تعديل الدفعة' : 'تسجيل دفعة جديدة';

    const modalHTML = `
      <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${modalTitle}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="paymentForm" novalidate>
                <div class="row">
                  <!-- Subscription Selection -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">الاشتراك <span class="text-danger">*</span></label>
                    <select class="form-select" name="subscription" required ${isEdit ? 'disabled' : ''}>
                      <option value="">اختر الاشتراك</option>
                      ${this.subscriptions.map(sub => `
                        <option value="${sub._id}"
                                data-amount="${sub.finalAmount || sub.amount}"
                                data-student="${sub.student?.fullName}"
                                ${payment?.subscription?._id === sub._id ? 'selected' : ''}>
                          ${sub.subscriptionId} - ${sub.student?.fullName} (${sub.finalAmount || sub.amount} ريال)
                        </option>
                      `).join('')}
                    </select>
                    <div class="invalid-feedback">يرجى اختيار الاشتراك</div>
                  </div>

                  <!-- Amount -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">المبلغ (ريال) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="amount" required min="0" step="0.01"
                           value="${payment?.amount || ''}">
                    <div class="invalid-feedback">المبلغ مطلوب</div>
                  </div>

                  <!-- Payment Method -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                    <select class="form-select" name="paymentMethod" required>
                      <option value="">اختر طريقة الدفع</option>
                      <option value="cash" ${payment?.paymentMethod === 'cash' ? 'selected' : ''}>نقدي</option>
                      <option value="card" ${payment?.paymentMethod === 'card' ? 'selected' : ''}>بطاقة</option>
                      <option value="bank_transfer" ${payment?.paymentMethod === 'bank_transfer' ? 'selected' : ''}>تحويل بنكي</option>
                      <option value="online" ${payment?.paymentMethod === 'online' ? 'selected' : ''}>دفع إلكتروني</option>
                      <option value="check" ${payment?.paymentMethod === 'check' ? 'selected' : ''}>شيك</option>
                    </select>
                    <div class="invalid-feedback">يرجى اختيار طريقة الدفع</div>
                  </div>

                  <!-- Payment Date -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" name="paymentDate" required
                           value="${payment?.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]}">
                    <div class="invalid-feedback">تاريخ الدفع مطلوب</div>
                  </div>

                  <!-- Transaction ID (for card/online payments) -->
                  <div class="col-md-6 mb-3" id="transactionIdGroup" style="display: none;">
                    <label class="form-label">رقم المعاملة</label>
                    <input type="text" class="form-control" name="transactionId"
                           value="${payment?.transactionId || ''}" placeholder="رقم المعاملة الإلكترونية">
                  </div>

                  <!-- Notes -->
                  <div class="col-12 mb-3">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-control" name="notes" rows="3" maxlength="500"
                              placeholder="أي ملاحظات إضافية...">${payment?.notes || ''}</textarea>
                    <div class="form-text">الحد الأقصى 500 حرف</div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-primary" id="savePaymentBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                ${isEdit ? 'تحديث' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('paymentModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();

    // Bind form events
    this.bindPaymentFormEvents(isEdit, payment);
  }

  bindPaymentFormEvents(isEdit, payment) {
    const form = document.getElementById('paymentForm');
    const saveBtn = document.getElementById('savePaymentBtn');
    const subscriptionSelect = form.querySelector('[name="subscription"]');
    const paymentMethodSelect = form.querySelector('[name="paymentMethod"]');
    const transactionIdGroup = document.getElementById('transactionIdGroup');

    if (!form || !saveBtn) return;

    // Subscription change handler
    subscriptionSelect.addEventListener('change', (e) => {
      const selectedOption = e.target.selectedOptions[0];
      if (selectedOption && selectedOption.value) {
        form.querySelector('[name="amount"]').value = selectedOption.dataset.amount;
      }
    });

    // Payment method change handler
    paymentMethodSelect.addEventListener('change', (e) => {
      const method = e.target.value;
      if (method === 'card' || method === 'online') {
        transactionIdGroup.style.display = 'block';
      } else {
        transactionIdGroup.style.display = 'none';
      }
    });

    // Form validation
    form.addEventListener('input', (e) => {
      this.validatePaymentField(e.target);
    });

    // Save button click
    saveBtn.addEventListener('click', async () => {
      if (this.validatePaymentForm(form)) {
        await this.savePayment(form, isEdit, payment);
      }
    });

    // Form submit
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      if (this.validatePaymentForm(form)) {
        await this.savePayment(form, isEdit, payment);
      }
    });

    // Initial setup
    if (paymentMethodSelect.value === 'card' || paymentMethodSelect.value === 'online') {
      transactionIdGroup.style.display = 'block';
    }
  }

  validatePaymentField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      message = 'هذا الحقل مطلوب';
    }

    // Number validation
    if (field.type === 'number' && value) {
      const num = parseFloat(value);
      const min = parseFloat(field.min);

      if (isNaN(num)) {
        isValid = false;
        message = 'يجب إدخال رقم صحيح';
      } else if (min !== undefined && num < min) {
        isValid = false;
        message = `القيمة يجب أن تكون ${min} أو أكثر`;
      }
    }

    // Update field state
    if (isValid) {
      field.classList.remove('is-invalid');
      field.classList.add('is-valid');
    } else {
      field.classList.remove('is-valid');
      field.classList.add('is-invalid');
      const feedback = field.nextElementSibling;
      if (feedback && feedback.classList.contains('invalid-feedback')) {
        feedback.textContent = message;
      }
    }

    return isValid;
  }

  validatePaymentForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!this.validatePaymentField(field)) {
        isValid = false;
      }
    });

    return isValid;
  }

  async savePayment(form, isEdit, existingPayment) {
    const saveBtn = document.getElementById('savePaymentBtn');
    const spinner = saveBtn.querySelector('.spinner-border');

    try {
      // Show loading
      saveBtn.disabled = true;
      spinner?.classList.remove('d-none');

      // Collect form data
      const formData = new FormData(form);
      const paymentData = {
        subscription: formData.get('subscription'),
        amount: parseFloat(formData.get('amount')),
        paymentMethod: formData.get('paymentMethod'),
        paymentDate: formData.get('paymentDate'),
        notes: formData.get('notes')
      };

      // Add transaction ID if provided
      const transactionId = formData.get('transactionId');
      if (transactionId) {
        paymentData.transactionId = transactionId;
      }

      // API call
      let response;
      if (isEdit) {
        response = await this.api.payments.update(existingPayment._id, paymentData);
      } else {
        response = await this.api.payments.create(paymentData);
      }

      if (response.success) {
        this.utils.showAlert(
          isEdit ? 'تم تحديث الدفعة بنجاح' : 'تم تسجيل الدفعة بنجاح',
          'success'
        );

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
        modal.hide();

        // Reload data
        await this.loadPayments();
        await this.loadStatistics();
        await this.loadInitialData(); // Refresh subscriptions list
      } else {
        this.utils.showAlert(response.message || 'حدث خطأ أثناء الحفظ', 'error');
      }
    } catch (error) {
      console.error('Error saving payment:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      // Hide loading
      saveBtn.disabled = false;
      spinner?.classList.add('d-none');
    }
  }
}

// Export for global access
window.PaymentsPage = PaymentsPage;
