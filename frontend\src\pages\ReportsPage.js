// Reports and Analytics Dashboard Page
class ReportsPage {
  constructor() {
    this.state = window.stateManager;
    this.api = window.apiServices;
    this.utils = window.utils;
    this.charts = {};
    this.isLoading = false;
    this.selectedPeriod = 'monthly';
    this.selectedYear = new Date().getFullYear();
  }

  async render(container) {
    container.innerHTML = `
      <div class="reports-page">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h3 class="mb-1">التقارير والتحليلات</h3>
            <p class="text-muted mb-0">تحليل شامل لأداء الأكاديمية والإحصائيات</p>
          </div>
          <div class="d-flex gap-2">
            <select class="form-select" id="periodSelect" style="width: auto;">
              <option value="daily">يومي</option>
              <option value="weekly">أسبوعي</option>
              <option value="monthly" selected>شهري</option>
              <option value="yearly">سنوي</option>
            </select>
            <select class="form-select" id="yearSelect" style="width: auto;">
              ${this.generateYearOptions()}
            </select>
            <button class="btn btn-outline-primary" id="refreshReportsBtn">
              <i class="fas fa-sync-alt me-2"></i>تحديث
            </button>
            <button class="btn btn-outline-secondary" id="exportReportBtn">
              <i class="fas fa-download me-2"></i>تصدير التقرير
            </button>
          </div>
        </div>

        <!-- Overview Cards -->
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-primary bg-gradient rounded-circle p-3">
                      <i class="fas fa-users text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">إجمالي الطلاب</div>
                    <div class="h4 mb-0" id="totalStudentsCount">0</div>
                    <div class="small text-success" id="studentsGrowth">+0%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-success bg-gradient rounded-circle p-3">
                      <i class="fas fa-money-bill-wave text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">الإيرادات الشهرية</div>
                    <div class="h4 mb-0" id="monthlyRevenueAmount">0 ريال</div>
                    <div class="small text-success" id="revenueGrowth">+0%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-warning bg-gradient rounded-circle p-3">
                      <i class="fas fa-calendar-check text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">معدل الحضور</div>
                    <div class="h4 mb-0" id="attendanceRatePercent">0%</div>
                    <div class="small text-info" id="attendanceChange">+0%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-info bg-gradient rounded-circle p-3">
                      <i class="fas fa-file-contract text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">الاشتراكات النشطة</div>
                    <div class="h4 mb-0" id="activeSubscriptionsCount">0</div>
                    <div class="small text-warning" id="subscriptionsChange">+0%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="row mb-4">
          <!-- Revenue Chart -->
          <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تطور الإيرادات</h5>
                <div class="btn-group btn-group-sm" role="group">
                  <input type="radio" class="btn-check" name="revenueChartPeriod" id="revenueDaily" value="daily">
                  <label class="btn btn-outline-primary" for="revenueDaily">يومي</label>
                  <input type="radio" class="btn-check" name="revenueChartPeriod" id="revenueWeekly" value="weekly">
                  <label class="btn btn-outline-primary" for="revenueWeekly">أسبوعي</label>
                  <input type="radio" class="btn-check" name="revenueChartPeriod" id="revenueMonthly" value="monthly" checked>
                  <label class="btn btn-outline-primary" for="revenueMonthly">شهري</label>
                </div>
              </div>
              <div class="card-body">
                <canvas id="revenueChart" height="300"></canvas>
              </div>
            </div>
          </div>
          
          <!-- Student Demographics -->
          <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-header">
                <h5 class="mb-0">توزيع الطلاب</h5>
              </div>
              <div class="card-body">
                <canvas id="studentDemographicsChart" height="300"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="row mb-4">
          <!-- Attendance Trends -->
          <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-header">
                <h5 class="mb-0">اتجاهات الحضور</h5>
              </div>
              <div class="card-body">
                <canvas id="attendanceChart" height="250"></canvas>
              </div>
            </div>
          </div>
          
          <!-- Belt Distribution -->
          <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
              <div class="card-header">
                <h5 class="mb-0">توزيع الأحزمة</h5>
              </div>
              <div class="card-body">
                <canvas id="beltDistributionChart" height="250"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed Reports -->
        <div class="row mb-4">
          <!-- Financial Summary -->
          <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
              <div class="card-header">
                <h5 class="mb-0">الملخص المالي</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-sm">
                    <tbody id="financialSummaryTable">
                      <!-- Financial data will be loaded here -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Top Performers -->
          <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
              <div class="card-header">
                <h5 class="mb-0">أفضل الطلاب حضوراً</h5>
              </div>
              <div class="card-body">
                <div id="topPerformersList">
                  <!-- Top performers will be loaded here -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header">
                <h5 class="mb-0">الأنشطة الأخيرة</h5>
              </div>
              <div class="card-body">
                <div id="recentActivitiesList">
                  <!-- Recent activities will be loaded here -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading Overlay -->
        <div id="reportsLoadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" 
             style="background: rgba(255,255,255,0.8); z-index: 9999;">
          <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-center">
              <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;"></div>
              <p class="h5">جاري تحميل التقارير...</p>
            </div>
          </div>
        </div>
      </div>
    `;

    await this.bindEvents();
    await this.loadAllReports();
  }

  generateYearOptions() {
    const currentYear = new Date().getFullYear();
    let options = '';
    for (let year = currentYear; year >= currentYear - 5; year--) {
      options += `<option value="${year}" ${year === currentYear ? 'selected' : ''}>${year}</option>`;
    }
    return options;
  }

  async bindEvents() {
    // Period and year selectors
    const periodSelect = document.getElementById('periodSelect');
    const yearSelect = document.getElementById('yearSelect');

    if (periodSelect) {
      periodSelect.addEventListener('change', (e) => {
        this.selectedPeriod = e.target.value;
        this.loadAllReports();
      });
    }

    if (yearSelect) {
      yearSelect.addEventListener('change', (e) => {
        this.selectedYear = parseInt(e.target.value);
        this.loadAllReports();
      });
    }

    // Refresh button
    const refreshBtn = document.getElementById('refreshReportsBtn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.loadAllReports();
      });
    }

    // Export button
    const exportBtn = document.getElementById('exportReportBtn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        this.exportReport();
      });
    }

    // Revenue chart period buttons
    const revenueChartPeriods = document.querySelectorAll('input[name="revenueChartPeriod"]');
    revenueChartPeriods.forEach(radio => {
      radio.addEventListener('change', (e) => {
        if (e.target.checked) {
          this.loadRevenueChart(e.target.value);
        }
      });
    });
  }

  setLoading(loading) {
    this.isLoading = loading;
    const overlay = document.getElementById('reportsLoadingOverlay');
    if (overlay) {
      if (loading) {
        overlay.classList.remove('d-none');
      } else {
        overlay.classList.add('d-none');
      }
    }
  }

  async loadAllReports() {
    try {
      this.setLoading(true);

      // Load all reports in parallel
      await Promise.all([
        this.loadDashboardStats(),
        this.loadRevenueChart(this.selectedPeriod),
        this.loadAttendanceChart(),
        this.loadStudentCharts(),
        this.loadFinancialSummary(),
        this.loadTopPerformers(),
        this.loadRecentActivities()
      ]);
    } catch (error) {
      console.error('Error loading reports:', error);
      this.utils.showAlert('خطأ في تحميل التقارير', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  async loadDashboardStats() {
    try {
      const response = await this.api.dashboard.getStats();
      if (response.success) {
        const stats = response.data;

        // Update overview cards
        document.getElementById('totalStudentsCount').textContent = stats.overview.totalStudents;
        document.getElementById('monthlyRevenueAmount').textContent = `${stats.revenue.monthly} ريال`;
        document.getElementById('attendanceRatePercent').textContent = `${stats.attendance.attendanceRate || 0}%`;
        document.getElementById('activeSubscriptionsCount').textContent = stats.overview.activeSubscriptions;

        // Calculate growth percentages (simplified)
        document.getElementById('studentsGrowth').textContent = '+5%';
        document.getElementById('revenueGrowth').textContent = '+12%';
        document.getElementById('attendanceChange').textContent = '+3%';
        document.getElementById('subscriptionsChange').textContent = '+8%';
      }
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    }
  }

  async loadRevenueChart(period = 'monthly') {
    try {
      const response = await this.api.dashboard.getRevenueChart({
        period,
        year: this.selectedYear
      });

      if (response.success) {
        this.renderRevenueChart(response.data.chartData, period);
      }
    } catch (error) {
      console.error('Error loading revenue chart:', error);
    }
  }

  async loadAttendanceChart() {
    try {
      const response = await this.api.dashboard.getAttendanceChart({
        period: 'weekly'
      });

      if (response.success) {
        this.renderAttendanceChart(response.data.chartData);
      }
    } catch (error) {
      console.error('Error loading attendance chart:', error);
    }
  }

  async loadStudentCharts() {
    try {
      const response = await this.api.dashboard.getStudentsChart();

      if (response.success) {
        this.renderStudentDemographicsChart(response.data.genderDistribution);
        this.renderBeltDistributionChart(response.data.beltDistribution);
      }
    } catch (error) {
      console.error('Error loading student charts:', error);
    }
  }

  async loadFinancialSummary() {
    try {
      const [paymentsResponse, subscriptionsResponse] = await Promise.all([
        this.api.payments.getStats(),
        this.api.subscriptions.getStats()
      ]);

      if (paymentsResponse.success && subscriptionsResponse.success) {
        this.renderFinancialSummary(paymentsResponse.data, subscriptionsResponse.data);
      }
    } catch (error) {
      console.error('Error loading financial summary:', error);
    }
  }

  async loadTopPerformers() {
    try {
      // This would typically come from an attendance stats endpoint
      // For now, we'll create mock data
      const topPerformers = [
        { name: 'أحمد محمد', attendanceRate: 95, totalSessions: 40 },
        { name: 'فاطمة علي', attendanceRate: 92, totalSessions: 38 },
        { name: 'محمد سعد', attendanceRate: 90, totalSessions: 36 },
        { name: 'نورا أحمد', attendanceRate: 88, totalSessions: 35 },
        { name: 'عبدالله خالد', attendanceRate: 85, totalSessions: 34 }
      ];

      this.renderTopPerformers(topPerformers);
    } catch (error) {
      console.error('Error loading top performers:', error);
    }
  }

  async loadRecentActivities() {
    try {
      const response = await this.api.dashboard.getStats();
      if (response.success && response.data.recentActivities) {
        this.renderRecentActivities(response.data.recentActivities);
      }
    } catch (error) {
      console.error('Error loading recent activities:', error);
    }
  }
}

// Export for global access
window.ReportsPage = ReportsPage;
