// ClassesPage Extensions - Additional methods for ClassesPage class
// This file extends the ClassesPage class with additional functionality

// Extend ClassesPage prototype with additional methods
Object.assign(ClassesPage.prototype, {
  
  // CRUD Operations
  async showClassModal(classItem = null) {
    const isEdit = !!classItem;
    const modalTitle = isEdit ? 'تعديل الصف' : 'إضافة صف جديد';
    
    const modalHTML = `
      <div class="modal fade" id="classModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${modalTitle}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="classForm" novalidate>
                <div class="row">
                  <!-- Basic Information -->
                  <div class="col-12 mb-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                    </h6>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">اسم الصف <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="name" required maxlength="100"
                           value="${classItem?.name || ''}" placeholder="مثال: تايكوندو للمبتدئين">
                    <div class="invalid-feedback">اسم الصف مطلوب</div>
                  </div>
                  
                  <div class="col-md-3 mb-3">
                    <label class="form-label">نوع الصف <span class="text-danger">*</span></label>
                    <select class="form-select" name="type" required>
                      <option value="">اختر النوع</option>
                      <option value="beginner" ${classItem?.type === 'beginner' ? 'selected' : ''}>مبتدئ</option>
                      <option value="intermediate" ${classItem?.type === 'intermediate' ? 'selected' : ''}>متوسط</option>
                      <option value="advanced" ${classItem?.type === 'advanced' ? 'selected' : ''}>متقدم</option>
                      <option value="competition" ${classItem?.type === 'competition' ? 'selected' : ''}>منافسات</option>
                      <option value="private" ${classItem?.type === 'private' ? 'selected' : ''}>خاص</option>
                      <option value="kids" ${classItem?.type === 'kids' ? 'selected' : ''}>أطفال</option>
                      <option value="adults" ${classItem?.type === 'adults' ? 'selected' : ''}>بالغين</option>
                    </select>
                    <div class="invalid-feedback">نوع الصف مطلوب</div>
                  </div>
                  
                  <div class="col-md-3 mb-3">
                    <label class="form-label">المستوى <span class="text-danger">*</span></label>
                    <select class="form-select" name="level" required>
                      <option value="">اختر المستوى</option>
                      <option value="white" ${classItem?.level === 'white' ? 'selected' : ''}>أبيض</option>
                      <option value="yellow" ${classItem?.level === 'yellow' ? 'selected' : ''}>أصفر</option>
                      <option value="orange" ${classItem?.level === 'orange' ? 'selected' : ''}>برتقالي</option>
                      <option value="green" ${classItem?.level === 'green' ? 'selected' : ''}>أخضر</option>
                      <option value="blue" ${classItem?.level === 'blue' ? 'selected' : ''}>أزرق</option>
                      <option value="brown" ${classItem?.level === 'brown' ? 'selected' : ''}>بني</option>
                      <option value="black" ${classItem?.level === 'black' ? 'selected' : ''}>أسود</option>
                      <option value="mixed" ${classItem?.level === 'mixed' ? 'selected' : ''}>مختلط</option>
                    </select>
                    <div class="invalid-feedback">المستوى مطلوب</div>
                  </div>
                  
                  <!-- Instructors -->
                  <div class="col-12 mt-3 mb-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-user-tie me-2"></i>المدربون
                    </h6>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">المدرب الرئيسي <span class="text-danger">*</span></label>
                    <select class="form-select" name="instructor" required>
                      <option value="">اختر المدرب</option>
                      ${this.instructors.map(instructor => `
                        <option value="${instructor._id}" ${classItem?.instructor?._id === instructor._id ? 'selected' : ''}>
                          ${instructor.fullName}
                        </option>
                      `).join('')}
                    </select>
                    <div class="invalid-feedback">المدرب الرئيسي مطلوب</div>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">المدرب المساعد</label>
                    <select class="form-select" name="assistantInstructor">
                      <option value="">اختر المدرب المساعد (اختياري)</option>
                      ${this.instructors.map(instructor => `
                        <option value="${instructor._id}" ${classItem?.assistantInstructor?._id === instructor._id ? 'selected' : ''}>
                          ${instructor.fullName}
                        </option>
                      `).join('')}
                    </select>
                  </div>
                  
                  <!-- Schedule -->
                  <div class="col-12 mt-3 mb-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-calendar-alt me-2"></i>الجدولة
                    </h6>
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <label class="form-label">يوم الأسبوع <span class="text-danger">*</span></label>
                    <select class="form-select" name="dayOfWeek" required>
                      <option value="">اختر اليوم</option>
                      <option value="0" ${classItem?.schedule?.dayOfWeek === 0 ? 'selected' : ''}>الأحد</option>
                      <option value="1" ${classItem?.schedule?.dayOfWeek === 1 ? 'selected' : ''}>الاثنين</option>
                      <option value="2" ${classItem?.schedule?.dayOfWeek === 2 ? 'selected' : ''}>الثلاثاء</option>
                      <option value="3" ${classItem?.schedule?.dayOfWeek === 3 ? 'selected' : ''}>الأربعاء</option>
                      <option value="4" ${classItem?.schedule?.dayOfWeek === 4 ? 'selected' : ''}>الخميس</option>
                      <option value="5" ${classItem?.schedule?.dayOfWeek === 5 ? 'selected' : ''}>الجمعة</option>
                      <option value="6" ${classItem?.schedule?.dayOfWeek === 6 ? 'selected' : ''}>السبت</option>
                    </select>
                    <div class="invalid-feedback">يوم الأسبوع مطلوب</div>
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <label class="form-label">وقت البداية <span class="text-danger">*</span></label>
                    <input type="time" class="form-control" name="startTime" required
                           value="${classItem?.schedule?.startTime || ''}">
                    <div class="invalid-feedback">وقت البداية مطلوب</div>
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <label class="form-label">وقت النهاية <span class="text-danger">*</span></label>
                    <input type="time" class="form-control" name="endTime" required
                           value="${classItem?.schedule?.endTime || ''}">
                    <div class="invalid-feedback">وقت النهاية مطلوب</div>
                  </div>
                  
                  <!-- Capacity & Age Range -->
                  <div class="col-12 mt-3 mb-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-users me-2"></i>السعة والفئة العمرية
                    </h6>
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <label class="form-label">سعة الصف <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="capacity" required min="1" max="50"
                           value="${classItem?.capacity || ''}">
                    <div class="invalid-feedback">سعة الصف مطلوبة (1-50)</div>
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <label class="form-label">الحد الأدنى للعمر</label>
                    <input type="number" class="form-control" name="ageMin" min="3" max="100"
                           value="${classItem?.ageRange?.min || ''}" placeholder="3">
                  </div>
                  
                  <div class="col-md-4 mb-3">
                    <label class="form-label">الحد الأقصى للعمر</label>
                    <input type="number" class="form-control" name="ageMax" min="3" max="100"
                           value="${classItem?.ageRange?.max || ''}" placeholder="100">
                  </div>
                  
                  <!-- Fees -->
                  <div class="col-12 mt-3 mb-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-money-bill-wave me-2"></i>الرسوم
                    </h6>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">الرسوم الشهرية (ريال)</label>
                    <input type="number" class="form-control" name="monthlyFee" min="0" step="0.01"
                           value="${classItem?.fees?.monthly || ''}" placeholder="300">
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">رسوم الجلسة الواحدة (ريال)</label>
                    <input type="number" class="form-control" name="perSessionFee" min="0" step="0.01"
                           value="${classItem?.fees?.perSession || ''}" placeholder="25">
                  </div>
                  
                  <!-- Description & Notes -->
                  <div class="col-12 mt-3 mb-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-file-alt me-2"></i>الوصف والملاحظات
                    </h6>
                  </div>
                  
                  <div class="col-12 mb-3">
                    <label class="form-label">وصف الصف</label>
                    <textarea class="form-control" name="description" rows="3" maxlength="500"
                              placeholder="وصف مختصر عن الصف وأهدافه...">${classItem?.description || ''}</textarea>
                    <div class="form-text">الحد الأقصى 500 حرف</div>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">المتطلبات</label>
                    <textarea class="form-control" name="requirements" rows="3"
                              placeholder="المتطلبات المطلوبة للانضمام للصف...">${classItem?.requirements?.join('\n') || ''}</textarea>
                    <div class="form-text">كل متطلب في سطر منفصل</div>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">المعدات المطلوبة</label>
                    <textarea class="form-control" name="equipment" rows="3"
                              placeholder="المعدات والأدوات المطلوبة...">${classItem?.equipment?.join('\n') || ''}</textarea>
                    <div class="form-text">كل معدة في سطر منفصل</div>
                  </div>
                  
                  <div class="col-12 mb-3">
                    <label class="form-label">ملاحظات إضافية</label>
                    <textarea class="form-control" name="notes" rows="2" maxlength="500"
                              placeholder="أي ملاحظات إضافية...">${classItem?.notes || ''}</textarea>
                  </div>
                  
                  <!-- Dates -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">تاريخ بداية الصف</label>
                    <input type="date" class="form-control" name="startDate"
                           value="${classItem?.startDate ? new Date(classItem.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]}">
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">تاريخ نهاية الصف</label>
                    <input type="date" class="form-control" name="endDate"
                           value="${classItem?.endDate ? new Date(classItem.endDate).toISOString().split('T')[0] : ''}">
                    <div class="form-text">اتركه فارغاً للصفوف المستمرة</div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-primary" id="saveClassBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                ${isEdit ? 'تحديث' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('classModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('classModal'));
    modal.show();

    // Bind form events
    this.bindClassFormEvents(isEdit, classItem);
  },

  bindClassFormEvents(isEdit, classItem) {
    const form = document.getElementById('classForm');
    const saveBtn = document.getElementById('saveClassBtn');

    if (!form || !saveBtn) return;

    // Form validation
    form.addEventListener('input', (e) => {
      this.validateClassField(e.target);
    });

    // Time validation
    const startTimeInput = form.querySelector('[name="startTime"]');
    const endTimeInput = form.querySelector('[name="endTime"]');

    [startTimeInput, endTimeInput].forEach(input => {
      if (input) {
        input.addEventListener('change', () => {
          this.validateTimeRange(startTimeInput, endTimeInput);
        });
      }
    });

    // Age range validation
    const ageMinInput = form.querySelector('[name="ageMin"]');
    const ageMaxInput = form.querySelector('[name="ageMax"]');

    [ageMinInput, ageMaxInput].forEach(input => {
      if (input) {
        input.addEventListener('change', () => {
          this.validateAgeRange(ageMinInput, ageMaxInput);
        });
      }
    });

    // Save button click
    saveBtn.addEventListener('click', async () => {
      if (this.validateClassForm(form)) {
        await this.saveClass(form, isEdit, classItem);
      }
    });

    // Form submit
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      if (this.validateClassForm(form)) {
        await this.saveClass(form, isEdit, classItem);
      }
    });
  },

  validateClassField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      message = 'هذا الحقل مطلوب';
    }

    // Number validation
    if (field.type === 'number' && value) {
      const num = parseInt(value);
      const min = parseInt(field.min);
      const max = parseInt(field.max);

      if (isNaN(num)) {
        isValid = false;
        message = 'يجب إدخال رقم صحيح';
      } else if (min !== undefined && num < min) {
        isValid = false;
        message = `القيمة يجب أن تكون ${min} أو أكثر`;
      } else if (max !== undefined && num > max) {
        isValid = false;
        message = `القيمة يجب أن تكون ${max} أو أقل`;
      }
    }

    // Update field state
    if (isValid) {
      field.classList.remove('is-invalid');
      field.classList.add('is-valid');
    } else {
      field.classList.remove('is-valid');
      field.classList.add('is-invalid');
      const feedback = field.nextElementSibling;
      if (feedback && feedback.classList.contains('invalid-feedback')) {
        feedback.textContent = message;
      }
    }

    return isValid;
  },

  validateTimeRange(startTimeInput, endTimeInput) {
    if (!startTimeInput.value || !endTimeInput.value) return true;

    const startTime = new Date(`2000-01-01T${startTimeInput.value}`);
    const endTime = new Date(`2000-01-01T${endTimeInput.value}`);

    if (endTime <= startTime) {
      endTimeInput.classList.add('is-invalid');
      const feedback = endTimeInput.nextElementSibling;
      if (feedback && feedback.classList.contains('invalid-feedback')) {
        feedback.textContent = 'وقت النهاية يجب أن يكون بعد وقت البداية';
      }
      return false;
    } else {
      endTimeInput.classList.remove('is-invalid');
      endTimeInput.classList.add('is-valid');
      return true;
    }
  },

  validateAgeRange(ageMinInput, ageMaxInput) {
    if (!ageMinInput.value || !ageMaxInput.value) return true;

    const minAge = parseInt(ageMinInput.value);
    const maxAge = parseInt(ageMaxInput.value);

    if (maxAge <= minAge) {
      ageMaxInput.classList.add('is-invalid');
      const feedback = ageMaxInput.nextElementSibling;
      if (feedback && feedback.classList.contains('invalid-feedback')) {
        feedback.textContent = 'الحد الأقصى للعمر يجب أن يكون أكبر من الحد الأدنى';
      }
      return false;
    } else {
      ageMaxInput.classList.remove('is-invalid');
      ageMaxInput.classList.add('is-valid');
      return true;
    }
  },

  validateClassForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!this.validateClassField(field)) {
        isValid = false;
      }
    });

    // Additional validations
    const startTimeInput = form.querySelector('[name="startTime"]');
    const endTimeInput = form.querySelector('[name="endTime"]');
    if (!this.validateTimeRange(startTimeInput, endTimeInput)) {
      isValid = false;
    }

    const ageMinInput = form.querySelector('[name="ageMin"]');
    const ageMaxInput = form.querySelector('[name="ageMax"]');
    if (!this.validateAgeRange(ageMinInput, ageMaxInput)) {
      isValid = false;
    }

    return isValid;
  },

  async saveClass(form, isEdit, existingClass) {
    const saveBtn = document.getElementById('saveClassBtn');
    const spinner = saveBtn.querySelector('.spinner-border');

    try {
      // Show loading
      saveBtn.disabled = true;
      spinner?.classList.remove('d-none');

      // Collect form data
      const formData = new FormData(form);
      const classData = {
        name: formData.get('name'),
        type: formData.get('type'),
        level: formData.get('level'),
        instructor: formData.get('instructor'),
        schedule: {
          dayOfWeek: parseInt(formData.get('dayOfWeek')),
          startTime: formData.get('startTime'),
          endTime: formData.get('endTime')
        },
        capacity: parseInt(formData.get('capacity')),
        description: formData.get('description'),
        notes: formData.get('notes'),
        startDate: formData.get('startDate'),
        endDate: formData.get('endDate') || null
      };

      // Add optional fields
      if (formData.get('assistantInstructor')) {
        classData.assistantInstructor = formData.get('assistantInstructor');
      }

      if (formData.get('ageMin') || formData.get('ageMax')) {
        classData.ageRange = {};
        if (formData.get('ageMin')) classData.ageRange.min = parseInt(formData.get('ageMin'));
        if (formData.get('ageMax')) classData.ageRange.max = parseInt(formData.get('ageMax'));
      }

      if (formData.get('monthlyFee') || formData.get('perSessionFee')) {
        classData.fees = {};
        if (formData.get('monthlyFee')) classData.fees.monthly = parseFloat(formData.get('monthlyFee'));
        if (formData.get('perSessionFee')) classData.fees.perSession = parseFloat(formData.get('perSessionFee'));
      }

      // Handle requirements and equipment arrays
      const requirements = formData.get('requirements');
      if (requirements) {
        classData.requirements = requirements.split('\n').filter(req => req.trim());
      }

      const equipment = formData.get('equipment');
      if (equipment) {
        classData.equipment = equipment.split('\n').filter(eq => eq.trim());
      }

      // API call
      let response;
      if (isEdit) {
        response = await this.api.classes.update(existingClass._id, classData);
      } else {
        response = await this.api.classes.create(classData);
      }

      if (response.success) {
        this.utils.showAlert(
          isEdit ? 'تم تحديث الصف بنجاح' : 'تم إنشاء الصف بنجاح',
          'success'
        );

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('classModal'));
        modal.hide();

        // Reload data
        await this.loadClasses();
        await this.loadStatistics();
      } else {
        this.utils.showAlert(response.message || 'حدث خطأ أثناء الحفظ', 'error');
      }
    } catch (error) {
      console.error('Error saving class:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      // Hide loading
      saveBtn.disabled = false;
      spinner?.classList.add('d-none');
    }
  }
});
