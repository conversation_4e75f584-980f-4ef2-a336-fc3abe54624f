// ReportsPage Extensions - Chart rendering and utility methods
// This file extends the ReportsPage class with chart functionality

// Extend ReportsPage prototype with chart methods
Object.assign(ReportsPage.prototype, {
  
  renderRevenueChart(data, period) {
    const ctx = document.getElementById('revenueChart');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (this.charts.revenue) {
      this.charts.revenue.destroy();
    }

    // Prepare data for Chart.js
    const labels = data.map(item => {
      if (period === 'monthly') {
        return `${item._id.month}/${item._id.year}`;
      } else if (period === 'daily') {
        return `${item._id.day}/${item._id.month}`;
      } else {
        return `الأسبوع ${item._id.week}`;
      }
    });

    const revenues = data.map(item => item.revenue || 0);

    this.charts.revenue = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'الإيرادات (ريال)',
          data: revenues,
          borderColor: '#0d6efd',
          backgroundColor: 'rgba(13, 110, 253, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value.toLocaleString() + ' ريال';
              }
            }
          }
        }
      }
    });
  },

  renderAttendanceChart(data) {
    const ctx = document.getElementById('attendanceChart');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (this.charts.attendance) {
      this.charts.attendance.destroy();
    }

    // Prepare data
    const labels = data.map(item => `${item._id.day}/${item._id.month}`);
    const presentData = data.map(item => item.present || 0);
    const absentData = data.map(item => item.absent || 0);
    const lateData = data.map(item => item.late || 0);

    this.charts.attendance = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [
          {
            label: 'حاضر',
            data: presentData,
            backgroundColor: '#198754',
            borderColor: '#198754',
            borderWidth: 1
          },
          {
            label: 'غائب',
            data: absentData,
            backgroundColor: '#dc3545',
            borderColor: '#dc3545',
            borderWidth: 1
          },
          {
            label: 'متأخر',
            data: lateData,
            backgroundColor: '#ffc107',
            borderColor: '#ffc107',
            borderWidth: 1
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top'
          }
        },
        scales: {
          x: {
            stacked: true
          },
          y: {
            stacked: true,
            beginAtZero: true
          }
        }
      }
    });
  },

  renderStudentDemographicsChart(genderData) {
    const ctx = document.getElementById('studentDemographicsChart');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (this.charts.demographics) {
      this.charts.demographics.destroy();
    }

    // Prepare data
    const labels = genderData.map(item => item._id === 'male' ? 'ذكور' : 'إناث');
    const counts = genderData.map(item => item.count);

    this.charts.demographics = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: labels,
        datasets: [{
          data: counts,
          backgroundColor: ['#0d6efd', '#e83e8c'],
          borderColor: ['#0d6efd', '#e83e8c'],
          borderWidth: 2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });
  },

  renderBeltDistributionChart(beltData) {
    const ctx = document.getElementById('beltDistributionChart');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (this.charts.belts) {
      this.charts.belts.destroy();
    }

    // Belt colors mapping
    const beltColors = {
      'white': '#ffffff',
      'yellow': '#ffc107',
      'orange': '#fd7e14',
      'green': '#198754',
      'blue': '#0d6efd',
      'brown': '#8b4513',
      'black': '#000000'
    };

    // Belt names in Arabic
    const beltNames = {
      'white': 'أبيض',
      'yellow': 'أصفر',
      'orange': 'برتقالي',
      'green': 'أخضر',
      'blue': 'أزرق',
      'brown': 'بني',
      'black': 'أسود'
    };

    // Prepare data
    const labels = beltData.map(item => beltNames[item._id] || item._id);
    const counts = beltData.map(item => item.count);
    const colors = beltData.map(item => beltColors[item._id] || '#6c757d');

    this.charts.belts = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'عدد الطلاب',
          data: counts,
          backgroundColor: colors,
          borderColor: colors.map(color => color === '#ffffff' ? '#000000' : color),
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        }
      }
    });
  },

  renderFinancialSummary(paymentsData, subscriptionsData) {
    const tableBody = document.getElementById('financialSummaryTable');
    if (!tableBody) return;

    const payments = paymentsData.overview || {};
    const subscriptions = subscriptionsData.overview || {};

    tableBody.innerHTML = `
      <tr>
        <td class="fw-medium">إجمالي الإيرادات</td>
        <td class="text-end fw-bold text-success">${payments.totalRevenue || 0} ريال</td>
      </tr>
      <tr>
        <td class="fw-medium">عدد المعاملات</td>
        <td class="text-end">${payments.totalTransactions || 0}</td>
      </tr>
      <tr>
        <td class="fw-medium">متوسط قيمة المعاملة</td>
        <td class="text-end">${Math.round((payments.totalRevenue || 0) / (payments.totalTransactions || 1))} ريال</td>
      </tr>
      <tr>
        <td class="fw-medium">المدفوعات المعلقة</td>
        <td class="text-end text-warning">${payments.pendingPayments || 0}</td>
      </tr>
      <tr>
        <td class="fw-medium">إجمالي الاستردادات</td>
        <td class="text-end text-danger">${payments.totalRefunds || 0} ريال</td>
      </tr>
      <tr class="border-top">
        <td class="fw-medium">الاشتراكات النشطة</td>
        <td class="text-end">${subscriptions.activeSubscriptions || 0}</td>
      </tr>
      <tr>
        <td class="fw-medium">الاشتراكات المنتهية</td>
        <td class="text-end text-muted">${subscriptions.expiredSubscriptions || 0}</td>
      </tr>
      <tr>
        <td class="fw-medium">متوسط قيمة الاشتراك</td>
        <td class="text-end">${subscriptions.averageAmount || 0} ريال</td>
      </tr>
    `;
  },

  renderTopPerformers(performers) {
    const container = document.getElementById('topPerformersList');
    if (!container) return;

    container.innerHTML = performers.map((performer, index) => `
      <div class="d-flex align-items-center mb-3">
        <div class="flex-shrink-0">
          <div class="avatar-sm bg-light rounded-circle d-flex align-items-center justify-content-center">
            <span class="fw-bold text-primary">${index + 1}</span>
          </div>
        </div>
        <div class="flex-grow-1 ms-3">
          <div class="fw-medium">${performer.name}</div>
          <div class="small text-muted">${performer.totalSessions} جلسة</div>
        </div>
        <div class="flex-shrink-0">
          <div class="text-end">
            <div class="fw-bold text-success">${performer.attendanceRate}%</div>
            <div class="progress" style="width: 60px; height: 4px;">
              <div class="progress-bar bg-success" style="width: ${performer.attendanceRate}%"></div>
            </div>
          </div>
        </div>
      </div>
    `).join('');
  },

  renderRecentActivities(activities) {
    const container = document.getElementById('recentActivitiesList');
    if (!container) return;

    if (!activities || activities.length === 0) {
      container.innerHTML = `
        <div class="text-center text-muted py-4">
          <i class="fas fa-history fa-2x mb-3"></i>
          <p>لا توجد أنشطة حديثة</p>
        </div>
      `;
      return;
    }

    container.innerHTML = activities.map(activity => `
      <div class="d-flex align-items-start mb-3">
        <div class="flex-shrink-0">
          <div class="avatar-sm bg-light rounded-circle d-flex align-items-center justify-content-center">
            <i class="fas ${this.getActivityIcon(activity.type)} text-primary"></i>
          </div>
        </div>
        <div class="flex-grow-1 ms-3">
          <div class="fw-medium">${activity.description}</div>
          <div class="small text-muted">${this.utils.formatDateTime(activity.timestamp)}</div>
        </div>
      </div>
    `).join('');
  },

  getActivityIcon(type) {
    const icons = {
      'student_registered': 'fa-user-plus',
      'payment_received': 'fa-money-bill-wave',
      'subscription_created': 'fa-file-contract',
      'attendance_marked': 'fa-calendar-check',
      'class_created': 'fa-chalkboard-teacher'
    };
    return icons[type] || 'fa-info-circle';
  },

  async exportReport() {
    try {
      // Create a comprehensive report
      const reportData = {
        generatedAt: new Date().toISOString(),
        period: this.selectedPeriod,
        year: this.selectedYear,
        // Add more report data here
      };

      // For now, show a simple alert
      this.utils.showAlert('تصدير التقرير قيد التطوير', 'info');
    } catch (error) {
      console.error('Error exporting report:', error);
      this.utils.showAlert('خطأ في تصدير التقرير', 'error');
    }
  }
});
