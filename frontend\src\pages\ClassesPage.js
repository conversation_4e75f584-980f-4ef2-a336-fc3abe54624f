// Classes Management Page
class ClassesPage {
  constructor() {
    this.state = window.stateManager;
    this.api = window.apiServices;
    this.utils = window.utils;
    this.currentPage = 1;
    this.pageSize = 10;
    this.searchQuery = '';
    this.filters = {};
    this.sortBy = 'schedule.dayOfWeek';
    this.sortOrder = 'asc';
    this.classes = [];
    this.totalClasses = 0;
    this.instructors = [];
    this.students = [];
    this.isLoading = false;
    this.daysOfWeek = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  }

  async render(container) {
    container.innerHTML = `
      <div class="classes-page">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h3 class="mb-1">إدارة الصفوف</h3>
            <p class="text-muted mb-0">إدارة جدولة الصفوف وتسجيل الطلاب</p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" id="scheduleViewBtn">
              <i class="fas fa-calendar-alt me-2"></i>عرض الجدول
            </button>
            <button class="btn btn-outline-secondary" id="exportClassesBtn">
              <i class="fas fa-download me-2"></i>تصدير البيانات
            </button>
            <button class="btn btn-primary" id="addClassBtn">
              <i class="fas fa-plus me-2"></i>إضافة صف جديد
            </button>
          </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-primary bg-gradient rounded-circle p-3">
                      <i class="fas fa-chalkboard-teacher text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">إجمالي الصفوف</div>
                    <div class="h4 mb-0" id="totalClassesCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-success bg-gradient rounded-circle p-3">
                      <i class="fas fa-play text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">الصفوف النشطة</div>
                    <div class="h4 mb-0" id="activeClassesCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-warning bg-gradient rounded-circle p-3">
                      <i class="fas fa-users text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">إجمالي الطلاب المسجلين</div>
                    <div class="h4 mb-0" id="totalEnrolledCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-info bg-gradient rounded-circle p-3">
                      <i class="fas fa-percentage text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">متوسط الإشغال</div>
                    <div class="h4 mb-0" id="averageOccupancy">0%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Search and Filters -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-3">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input type="text" class="form-control" id="searchInput" 
                         placeholder="البحث باسم الصف أو المدرب...">
                </div>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="typeFilter">
                  <option value="">جميع الأنواع</option>
                  <option value="beginner">مبتدئ</option>
                  <option value="intermediate">متوسط</option>
                  <option value="advanced">متقدم</option>
                  <option value="competition">منافسات</option>
                  <option value="private">خاص</option>
                  <option value="kids">أطفال</option>
                  <option value="adults">بالغين</option>
                </select>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="levelFilter">
                  <option value="">جميع المستويات</option>
                  <option value="white">أبيض</option>
                  <option value="yellow">أصفر</option>
                  <option value="orange">برتقالي</option>
                  <option value="green">أخضر</option>
                  <option value="blue">أزرق</option>
                  <option value="brown">بني</option>
                  <option value="black">أسود</option>
                  <option value="mixed">مختلط</option>
                </select>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="dayFilter">
                  <option value="">جميع الأيام</option>
                  <option value="0">الأحد</option>
                  <option value="1">الاثنين</option>
                  <option value="2">الثلاثاء</option>
                  <option value="3">الأربعاء</option>
                  <option value="4">الخميس</option>
                  <option value="5">الجمعة</option>
                  <option value="6">السبت</option>
                </select>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="instructorFilter">
                  <option value="">جميع المدربين</option>
                  <!-- Will be populated dynamically -->
                </select>
              </div>
              <div class="col-md-1">
                <button class="btn btn-outline-secondary w-100" id="clearFiltersBtn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Classes Table -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة الصفوف</h5>
            <div class="d-flex align-items-center gap-3">
              <div class="d-flex align-items-center">
                <label class="form-label mb-0 me-2">عرض:</label>
                <select class="form-select form-select-sm" id="pageSizeSelect" style="width: auto;">
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>اسم الصف</th>
                    <th>النوع/المستوى</th>
                    <th>المدرب</th>
                    <th>الجدولة</th>
                    <th>السعة</th>
                    <th>المسجلون</th>
                    <th>الرسوم</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody id="classesTableBody">
                  <!-- Classes will be loaded here -->
                </tbody>
              </table>
            </div>
            
            <!-- Loading State -->
            <div id="loadingState" class="text-center py-5 d-none">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
              </div>
              <p class="mt-3 text-muted">جاري تحميل الصفوف...</p>
            </div>
            
            <!-- Empty State -->
            <div id="emptyState" class="text-center py-5 d-none">
              <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا توجد صفوف</h5>
              <p class="text-muted">لم يتم العثور على صفوف مطابقة للبحث أو الفلاتر المحددة</p>
              <button class="btn btn-primary" id="addFirstClassBtn">
                <i class="fas fa-plus me-2"></i>إضافة أول صف
              </button>
            </div>
          </div>
          
          <!-- Pagination -->
          <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div class="text-muted">
                عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span> 
                من أصل <span id="totalCount">0</span> صف
              </div>
              <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                  <!-- Pagination will be generated here -->
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    `;

    await this.bindEvents();
    await this.loadInitialData();
    await this.loadClasses();
    await this.loadStatistics();
  }

  async bindEvents() {
    // Search input with debounce
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.searchQuery = e.target.value;
          this.currentPage = 1;
          this.loadClasses();
        }, 300);
      });
    }

    // Filter changes
    ['typeFilter', 'levelFilter', 'dayFilter', 'instructorFilter'].forEach(filterId => {
      const filter = document.getElementById(filterId);
      if (filter) {
        filter.addEventListener('change', (e) => {
          const filterKey = filterId.replace('Filter', '');
          if (e.target.value) {
            this.filters[filterKey] = e.target.value;
          } else {
            delete this.filters[filterKey];
          }
          this.currentPage = 1;
          this.loadClasses();
        });
      }
    });

    // Clear filters
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', () => {
        this.clearFilters();
      });
    }

    // Page size change
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
      pageSizeSelect.addEventListener('change', (e) => {
        this.pageSize = parseInt(e.target.value);
        this.currentPage = 1;
        this.loadClasses();
      });
    }

    // Action buttons
    const addClassBtn = document.getElementById('addClassBtn');
    const addFirstClassBtn = document.getElementById('addFirstClassBtn');
    const scheduleViewBtn = document.getElementById('scheduleViewBtn');
    const exportClassesBtn = document.getElementById('exportClassesBtn');

    if (addClassBtn) {
      addClassBtn.addEventListener('click', () => this.showClassModal());
    }
    if (addFirstClassBtn) {
      addFirstClassBtn.addEventListener('click', () => this.showClassModal());
    }
    if (scheduleViewBtn) {
      scheduleViewBtn.addEventListener('click', () => this.showScheduleView());
    }
    if (exportClassesBtn) {
      exportClassesBtn.addEventListener('click', () => this.exportClasses());
    }

    // Table row actions (using event delegation)
    const tableBody = document.getElementById('classesTableBody');
    if (tableBody) {
      tableBody.addEventListener('click', (e) => {
        const classId = e.target.closest('tr')?.dataset.classId;
        if (!classId) return;

        if (e.target.closest('.btn-view')) {
          this.viewClass(classId);
        } else if (e.target.closest('.btn-edit')) {
          this.editClass(classId);
        } else if (e.target.closest('.btn-students')) {
          this.manageStudents(classId);
        } else if (e.target.closest('.btn-toggle')) {
          this.toggleClassStatus(classId);
        } else if (e.target.closest('.btn-delete')) {
          this.deleteClass(classId);
        }
      });
    }

    // Pagination (using event delegation)
    const pagination = document.getElementById('pagination');
    if (pagination) {
      pagination.addEventListener('click', (e) => {
        e.preventDefault();
        const page = e.target.dataset.page;
        if (page && page !== this.currentPage.toString()) {
          this.currentPage = parseInt(page);
          this.loadClasses();
        }
      });
    }
  }

  clearFilters() {
    this.searchQuery = '';
    this.filters = {};
    this.currentPage = 1;

    // Reset form elements
    document.getElementById('searchInput').value = '';
    document.getElementById('typeFilter').value = '';
    document.getElementById('levelFilter').value = '';
    document.getElementById('dayFilter').value = '';
    document.getElementById('instructorFilter').value = '';

    this.loadClasses();
  }

  async loadInitialData() {
    try {
      // Load instructors and students
      const [instructorsResponse, studentsResponse] = await Promise.all([
        this.api.users.getAll({ limit: 100, role: 'coach' }),
        this.api.students.getAll({ limit: 1000, status: 'active' })
      ]);

      if (instructorsResponse.success) {
        this.instructors = instructorsResponse.data.users;
        this.populateInstructorFilter();
      }

      if (studentsResponse.success) {
        this.students = studentsResponse.data.students;
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  }

  populateInstructorFilter() {
    const instructorFilter = document.getElementById('instructorFilter');
    if (!instructorFilter) return;

    // Keep the "جميع المدربين" option and add instructors
    const currentOptions = instructorFilter.innerHTML;
    const instructorOptions = this.instructors.map(instructor =>
      `<option value="${instructor._id}">${instructor.fullName}</option>`
    ).join('');

    instructorFilter.innerHTML = currentOptions + instructorOptions;
  }

  async loadClasses() {
    try {
      this.setLoading(true);

      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        sortBy: this.sortBy,
        sortOrder: this.sortOrder
      };

      if (this.searchQuery) {
        params.search = this.searchQuery;
      }

      Object.assign(params, this.filters);

      const response = await this.api.classes.getAll(params);

      if (response.success) {
        this.classes = response.data.classes;
        this.totalClasses = response.data.pagination.totalClasses;
        this.renderClassesTable();
        this.renderPagination();
        this.updatePaginationInfo();
      } else {
        this.utils.showAlert('خطأ في تحميل الصفوف', 'error');
      }
    } catch (error) {
      console.error('Error loading classes:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  async loadStatistics() {
    try {
      const response = await this.api.classes.getStats();
      if (response.success) {
        const stats = response.data;
        document.getElementById('totalClassesCount').textContent = stats.totalClasses || 0;
        document.getElementById('activeClassesCount').textContent = stats.activeClasses || 0;
        document.getElementById('totalEnrolledCount').textContent = stats.totalEnrolled || 0;
        document.getElementById('averageOccupancy').textContent = `${stats.averageOccupancy || 0}%`;
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    const loadingState = document.getElementById('loadingState');
    const tableBody = document.getElementById('classesTableBody');

    if (loading) {
      loadingState?.classList.remove('d-none');
      tableBody?.classList.add('d-none');
    } else {
      loadingState?.classList.add('d-none');
      tableBody?.classList.remove('d-none');
    }
  }

  renderClassesTable() {
    const tableBody = document.getElementById('classesTableBody');
    const emptyState = document.getElementById('emptyState');

    if (!tableBody) return;

    if (this.classes.length === 0) {
      tableBody.classList.add('d-none');
      emptyState?.classList.remove('d-none');
      return;
    }

    emptyState?.classList.add('d-none');
    tableBody.classList.remove('d-none');

    tableBody.innerHTML = this.classes.map(classItem => `
      <tr data-class-id="${classItem._id}">
        <td>
          <div class="fw-medium">${classItem.name}</div>
          <small class="text-muted">${classItem.description || 'لا يوجد وصف'}</small>
        </td>
        <td>
          <div>
            <span class="badge ${this.getTypeColor(classItem.type)}">
              ${this.getTypeText(classItem.type)}
            </span>
          </div>
          <div class="mt-1">
            <span class="badge ${this.getLevelColor(classItem.level)}">
              ${this.getLevelText(classItem.level)}
            </span>
          </div>
        </td>
        <td>
          <div class="fw-medium">${classItem.instructor?.fullName || 'غير محدد'}</div>
          ${classItem.assistantInstructor ? `
            <small class="text-muted">مساعد: ${classItem.assistantInstructor.fullName}</small>
          ` : ''}
        </td>
        <td>
          <div class="fw-medium">${this.daysOfWeek[classItem.schedule.dayOfWeek]}</div>
          <small class="text-muted">
            ${classItem.schedule.startTime} - ${classItem.schedule.endTime}
          </small>
        </td>
        <td>
          <div class="fw-medium">${classItem.capacity} طالب</div>
          ${classItem.ageRange?.min && classItem.ageRange?.max ? `
            <small class="text-muted">${classItem.ageRange.min}-${classItem.ageRange.max} سنة</small>
          ` : ''}
        </td>
        <td>
          <div class="d-flex align-items-center">
            <div class="progress me-2" style="width: 60px; height: 8px;">
              <div class="progress-bar ${this.getOccupancyColor(classItem.currentEnrollment, classItem.capacity)}"
                   style="width: ${(classItem.currentEnrollment / classItem.capacity) * 100}%"></div>
            </div>
            <small class="fw-medium">${classItem.currentEnrollment}/${classItem.capacity}</small>
          </div>
          <small class="text-muted">${classItem.availableSpots} متاح</small>
        </td>
        <td>
          ${classItem.fees?.monthly ? `
            <div class="fw-medium">${classItem.fees.monthly} ريال/شهر</div>
          ` : ''}
          ${classItem.fees?.perSession ? `
            <small class="text-muted">${classItem.fees.perSession} ريال/جلسة</small>
          ` : ''}
        </td>
        <td>
          <span class="badge ${classItem.isActive ? 'bg-success' : 'bg-secondary'}">
            ${classItem.isActive ? 'نشط' : 'غير نشط'}
          </span>
        </td>
        <td>
          <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary btn-view" title="عرض التفاصيل">
              <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-outline-warning btn-edit" title="تعديل">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-outline-info btn-students" title="إدارة الطلاب">
              <i class="fas fa-users"></i>
            </button>
            <button class="btn btn-outline-${classItem.isActive ? 'secondary' : 'success'} btn-toggle"
                    title="${classItem.isActive ? 'إيقاف' : 'تفعيل'}">
              <i class="fas fa-${classItem.isActive ? 'pause' : 'play'}"></i>
            </button>
            <button class="btn btn-outline-danger btn-delete" title="حذف">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `).join('');
  }

  getTypeColor(type) {
    const colors = {
      'beginner': 'bg-success',
      'intermediate': 'bg-warning',
      'advanced': 'bg-danger',
      'competition': 'bg-primary',
      'private': 'bg-info',
      'kids': 'bg-pink',
      'adults': 'bg-secondary'
    };
    return colors[type] || 'bg-secondary';
  }

  getTypeText(type) {
    const texts = {
      'beginner': 'مبتدئ',
      'intermediate': 'متوسط',
      'advanced': 'متقدم',
      'competition': 'منافسات',
      'private': 'خاص',
      'kids': 'أطفال',
      'adults': 'بالغين'
    };
    return texts[type] || type;
  }

  getLevelColor(level) {
    const colors = {
      'white': 'bg-light text-dark',
      'yellow': 'bg-warning',
      'orange': 'bg-warning',
      'green': 'bg-success',
      'blue': 'bg-primary',
      'brown': 'bg-secondary',
      'black': 'bg-dark',
      'mixed': 'bg-info'
    };
    return colors[level] || 'bg-secondary';
  }

  getLevelText(level) {
    const texts = {
      'white': 'أبيض',
      'yellow': 'أصفر',
      'orange': 'برتقالي',
      'green': 'أخضر',
      'blue': 'أزرق',
      'brown': 'بني',
      'black': 'أسود',
      'mixed': 'مختلط'
    };
    return texts[level] || level;
  }

  getOccupancyColor(enrolled, capacity) {
    const percentage = (enrolled / capacity) * 100;
    if (percentage >= 90) return 'bg-danger';
    if (percentage >= 75) return 'bg-warning';
    return 'bg-success';
  }

  renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    const totalPages = Math.ceil(this.totalClasses / this.pageSize);

    if (totalPages <= 1) {
      pagination.innerHTML = '';
      return;
    }

    let paginationHTML = '';

    // Previous button
    paginationHTML += `
      <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage - 1}">
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
    `;

    // Page numbers
    const startPage = Math.max(1, this.currentPage - 2);
    const endPage = Math.min(totalPages, this.currentPage + 2);

    if (startPage > 1) {
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="1">1</a>
        </li>
      `;
      if (startPage > 2) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
        <li class="page-item ${i === this.currentPage ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
        </li>
      `;
    }

    // Next button
    paginationHTML += `
      <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage + 1}">
          <i class="fas fa-chevron-left"></i>
        </a>
      </li>
    `;

    pagination.innerHTML = paginationHTML;
  }

  updatePaginationInfo() {
    const showingFrom = document.getElementById('showingFrom');
    const showingTo = document.getElementById('showingTo');
    const totalCount = document.getElementById('totalCount');

    if (showingFrom && showingTo && totalCount) {
      const from = (this.currentPage - 1) * this.pageSize + 1;
      const to = Math.min(this.currentPage * this.pageSize, this.totalClasses);

      showingFrom.textContent = this.totalClasses > 0 ? from : 0;
      showingTo.textContent = to;
      totalCount.textContent = this.totalClasses;
    }
  }
}

// Export for global access
window.ClassesPage = ClassesPage;
