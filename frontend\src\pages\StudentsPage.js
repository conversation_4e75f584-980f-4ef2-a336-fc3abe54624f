// Students Management Page
class StudentsPage {
  constructor() {
    this.state = window.stateManager;
    this.api = window.apiServices;
    this.utils = window.utils;
    this.currentPage = 1;
    this.pageSize = 10;
    this.searchQuery = '';
    this.filters = {};
    this.sortBy = 'enrollmentDate';
    this.sortOrder = 'desc';
    this.students = [];
    this.totalStudents = 0;
    this.selectedStudent = null;
    this.isLoading = false;
  }

  async render(container) {
    container.innerHTML = `
      <div class="students-page">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h3 class="mb-1">إدارة الطلاب</h3>
            <p class="text-muted mb-0">إدارة بيانات الطلاب والتسجيل</p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" id="exportBtn">
              <i class="fas fa-download me-2"></i>تصدير البيانات
            </button>
            <button class="btn btn-primary" id="addStudentBtn">
              <i class="fas fa-plus me-2"></i>إضافة طالب جديد
            </button>
          </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-primary bg-gradient rounded-circle p-3">
                      <i class="fas fa-users text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">إجمالي الطلاب</div>
                    <div class="h4 mb-0" id="totalStudentsCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-success bg-gradient rounded-circle p-3">
                      <i class="fas fa-user-check text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">الطلاب النشطون</div>
                    <div class="h4 mb-0" id="activeStudentsCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-warning bg-gradient rounded-circle p-3">
                      <i class="fas fa-medal text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">الأحزمة السوداء</div>
                    <div class="h4 mb-0" id="blackBeltsCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-info bg-gradient rounded-circle p-3">
                      <i class="fas fa-calendar-plus text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">مسجلون هذا الشهر</div>
                    <div class="h4 mb-0" id="newStudentsCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Search and Filters -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-4">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input type="text" class="form-control" id="searchInput"
                         placeholder="البحث بالاسم أو رقم الطالب...">
                </div>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                  <option value="">جميع الحالات</option>
                  <option value="active">نشط</option>
                  <option value="inactive">غير نشط</option>
                  <option value="suspended">موقوف</option>
                  <option value="graduated">متخرج</option>
                </select>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="beltFilter">
                  <option value="">جميع الأحزمة</option>
                  <option value="أبيض">أبيض</option>
                  <option value="أصفر">أصفر</option>
                  <option value="برتقالي">برتقالي</option>
                  <option value="أخضر">أخضر</option>
                  <option value="أزرق">أزرق</option>
                  <option value="بني">بني</option>
                  <option value="أسود">أسود</option>
                </select>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="genderFilter">
                  <option value="">جميع الأجناس</option>
                  <option value="male">ذكر</option>
                  <option value="female">أنثى</option>
                </select>
              </div>
              <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" id="clearFiltersBtn">
                  <i class="fas fa-times me-2"></i>مسح الفلاتر
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Students Table -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة الطلاب</h5>
            <div class="d-flex align-items-center gap-3">
              <div class="d-flex align-items-center">
                <label class="form-label mb-0 me-2">عرض:</label>
                <select class="form-select form-select-sm" id="pageSizeSelect" style="width: auto;">
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
              <div class="d-flex align-items-center">
                <label class="form-label mb-0 me-2">ترتيب:</label>
                <select class="form-select form-select-sm" id="sortSelect" style="width: auto;">
                  <option value="enrollmentDate:desc">الأحدث تسجيلاً</option>
                  <option value="enrollmentDate:asc">الأقدم تسجيلاً</option>
                  <option value="firstName:asc">الاسم (أ-ي)</option>
                  <option value="firstName:desc">الاسم (ي-أ)</option>
                  <option value="age:asc">العمر (الأصغر)</option>
                  <option value="age:desc">العمر (الأكبر)</option>
                </select>
              </div>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>رقم الطالب</th>
                    <th>الاسم الكامل</th>
                    <th>العمر</th>
                    <th>الجنس</th>
                    <th>مستوى الحزام</th>
                    <th>الهاتف</th>
                    <th>تاريخ التسجيل</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody id="studentsTableBody">
                  <!-- Students will be loaded here -->
                </tbody>
              </table>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-5 d-none">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
              </div>
              <p class="mt-3 text-muted">جاري تحميل بيانات الطلاب...</p>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="text-center py-5 d-none">
              <i class="fas fa-users fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا توجد بيانات طلاب</h5>
              <p class="text-muted">لم يتم العثور على أي طلاب مطابقين للبحث أو الفلاتر المحددة</p>
              <button class="btn btn-primary" id="addFirstStudentBtn">
                <i class="fas fa-plus me-2"></i>إضافة أول طالب
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div class="text-muted">
                عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span>
                من أصل <span id="totalCount">0</span> طالب
              </div>
              <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                  <!-- Pagination will be generated here -->
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    `;

    await this.bindEvents();
    await this.loadStudents();
    await this.loadStatistics();
  }

  async bindEvents() {
    // Search input with debounce
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.searchQuery = e.target.value;
          this.currentPage = 1;
          this.loadStudents();
        }, 300);
      });
    }

    // Filter changes
    ['statusFilter', 'beltFilter', 'genderFilter'].forEach(filterId => {
      const filter = document.getElementById(filterId);
      if (filter) {
        filter.addEventListener('change', (e) => {
          const filterKey = filterId.replace('Filter', '');
          if (e.target.value) {
            this.filters[filterKey] = e.target.value;
          } else {
            delete this.filters[filterKey];
          }
          this.currentPage = 1;
          this.loadStudents();
        });
      }
    });

    // Clear filters
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', () => {
        this.clearFilters();
      });
    }

    // Page size change
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
      pageSizeSelect.addEventListener('change', (e) => {
        this.pageSize = parseInt(e.target.value);
        this.currentPage = 1;
        this.loadStudents();
      });
    }

    // Sort change
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
      sortSelect.addEventListener('change', (e) => {
        const [sortBy, sortOrder] = e.target.value.split(':');
        this.sortBy = sortBy;
        this.sortOrder = sortOrder;
        this.loadStudents();
      });
    }

    // Add student buttons
    const addStudentBtn = document.getElementById('addStudentBtn');
    const addFirstStudentBtn = document.getElementById('addFirstStudentBtn');
    if (addStudentBtn) {
      addStudentBtn.addEventListener('click', () => this.showStudentModal());
    }
    if (addFirstStudentBtn) {
      addFirstStudentBtn.addEventListener('click', () => this.showStudentModal());
    }

    // Export button
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.exportStudents());
    }

    // Table row actions (using event delegation)
    const tableBody = document.getElementById('studentsTableBody');
    if (tableBody) {
      tableBody.addEventListener('click', (e) => {
        const studentId = e.target.closest('tr')?.dataset.studentId;
        if (!studentId) return;

        if (e.target.closest('.btn-view')) {
          this.viewStudent(studentId);
        } else if (e.target.closest('.btn-edit')) {
          this.editStudent(studentId);
        } else if (e.target.closest('.btn-delete')) {
          this.deleteStudent(studentId);
        } else if (e.target.closest('.btn-belt')) {
          this.updateBelt(studentId);
        }
      });
    }

    // Pagination (using event delegation)
    const pagination = document.getElementById('pagination');
    if (pagination) {
      pagination.addEventListener('click', (e) => {
        e.preventDefault();
        const page = e.target.dataset.page;
        if (page && page !== this.currentPage.toString()) {
          this.currentPage = parseInt(page);
          this.loadStudents();
        }
      });
    }
  }

  clearFilters() {
    this.searchQuery = '';
    this.filters = {};
    this.currentPage = 1;

    // Reset form elements
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('beltFilter').value = '';
    document.getElementById('genderFilter').value = '';

    this.loadStudents();
  }

  async loadStudents() {
    try {
      this.setLoading(true);

      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        sortBy: this.sortBy,
        sortOrder: this.sortOrder
      };

      if (this.searchQuery) {
        params.search = this.searchQuery;
      }

      Object.assign(params, this.filters);

      const response = await this.api.students.getAll(params);

      if (response.success) {
        this.students = response.data.students;
        this.totalStudents = response.data.total;
        this.renderStudentsTable();
        this.renderPagination();
        this.updatePaginationInfo();
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الطلاب', 'error');
      }
    } catch (error) {
      console.error('Error loading students:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  async loadStatistics() {
    try {
      const response = await this.api.students.getStats();
      if (response.success) {
        const stats = response.data.stats;
        document.getElementById('totalStudentsCount').textContent = stats.totalStudents || 0;
        document.getElementById('activeStudentsCount').textContent = stats.activeStudents || 0;
        document.getElementById('blackBeltsCount').textContent = stats.blackBelts || 0;
        document.getElementById('newStudentsCount').textContent = stats.newThisMonth || 0;
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    const loadingState = document.getElementById('loadingState');
    const tableBody = document.getElementById('studentsTableBody');

    if (loading) {
      loadingState?.classList.remove('d-none');
      tableBody?.classList.add('d-none');
    } else {
      loadingState?.classList.add('d-none');
      tableBody?.classList.remove('d-none');
    }
  }

  renderStudentsTable() {
    const tableBody = document.getElementById('studentsTableBody');
    const emptyState = document.getElementById('emptyState');

    if (!tableBody) return;

    if (this.students.length === 0) {
      tableBody.classList.add('d-none');
      emptyState?.classList.remove('d-none');
      return;
    }

    emptyState?.classList.add('d-none');
    tableBody.classList.remove('d-none');

    tableBody.innerHTML = this.students.map(student => `
      <tr data-student-id="${student._id}">
        <td>
          <span class="fw-medium">${student.studentId}</span>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
              <i class="fas fa-user text-muted"></i>
            </div>
            <div>
              <div class="fw-medium">${student.fullName}</div>
              <small class="text-muted">${student.email || 'لا يوجد بريد إلكتروني'}</small>
            </div>
          </div>
        </td>
        <td>${student.age} سنة</td>
        <td>
          <span class="badge ${student.gender === 'male' ? 'bg-primary' : 'bg-pink'}">
            ${student.gender === 'male' ? 'ذكر' : 'أنثى'}
          </span>
        </td>
        <td>
          <span class="badge ${this.getBeltColor(student.beltLevel)}">
            ${student.beltLevel}
          </span>
        </td>
        <td>
          <a href="tel:${student.phone}" class="text-decoration-none">
            ${student.phone}
          </a>
        </td>
        <td>
          <small class="text-muted">
            ${this.utils.formatDate(student.enrollmentDate)}
          </small>
        </td>
        <td>
          <span class="badge ${this.getStatusColor(student.status)}">
            ${this.getStatusText(student.status)}
          </span>
        </td>
        <td>
          <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary btn-view" title="عرض التفاصيل">
              <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-outline-warning btn-edit" title="تعديل">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-outline-info btn-belt" title="تحديث الحزام">
              <i class="fas fa-medal"></i>
            </button>
            <button class="btn btn-outline-danger btn-delete" title="حذف">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `).join('');
  }

  getBeltColor(beltLevel) {
    const colors = {
      'أبيض': 'bg-light text-dark',
      'أصفر': 'bg-warning',
      'برتقالي': 'bg-warning',
      'أخضر': 'bg-success',
      'أزرق': 'bg-primary',
      'بني': 'bg-secondary',
      'أسود': 'bg-dark'
    };
    return colors[beltLevel] || 'bg-secondary';
  }

  getStatusColor(status) {
    const colors = {
      'active': 'bg-success',
      'inactive': 'bg-secondary',
      'suspended': 'bg-warning',
      'graduated': 'bg-info'
    };
    return colors[status] || 'bg-secondary';
  }

  getStatusText(status) {
    const texts = {
      'active': 'نشط',
      'inactive': 'غير نشط',
      'suspended': 'موقوف',
      'graduated': 'متخرج'
    };
    return texts[status] || status;
  }

  renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    const totalPages = Math.ceil(this.totalStudents / this.pageSize);

    if (totalPages <= 1) {
      pagination.innerHTML = '';
      return;
    }

    let paginationHTML = '';

    // Previous button
    paginationHTML += `
      <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage - 1}">
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
    `;

    // Page numbers
    const startPage = Math.max(1, this.currentPage - 2);
    const endPage = Math.min(totalPages, this.currentPage + 2);

    if (startPage > 1) {
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="1">1</a>
        </li>
      `;
      if (startPage > 2) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
        <li class="page-item ${i === this.currentPage ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
        </li>
      `;
    }

    // Next button
    paginationHTML += `
      <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage + 1}">
          <i class="fas fa-chevron-left"></i>
        </a>
      </li>
    `;

    pagination.innerHTML = paginationHTML;
  }

  updatePaginationInfo() {
    const showingFrom = document.getElementById('showingFrom');
    const showingTo = document.getElementById('showingTo');
    const totalCount = document.getElementById('totalCount');

    if (showingFrom && showingTo && totalCount) {
      const from = (this.currentPage - 1) * this.pageSize + 1;
      const to = Math.min(this.currentPage * this.pageSize, this.totalStudents);

      showingFrom.textContent = this.totalStudents > 0 ? from : 0;
      showingTo.textContent = to;
      totalCount.textContent = this.totalStudents;
    }
  }

  // CRUD Operations
  async showStudentModal(student = null) {
    const isEdit = !!student;
    const modalTitle = isEdit ? 'تعديل بيانات الطالب' : 'إضافة طالب جديد';

    const modalHTML = `
      <div class="modal fade" id="studentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${modalTitle}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="studentForm" novalidate>
                <div class="row">
                  <!-- Personal Information -->
                  <div class="col-12">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-user me-2"></i>المعلومات الشخصية
                    </h6>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="firstName" required
                           value="${student?.firstName || ''}" maxlength="50">
                    <div class="invalid-feedback"></div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">اسم العائلة <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="lastName" required
                           value="${student?.lastName || ''}" maxlength="50">
                    <div class="invalid-feedback"></div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">تاريخ الميلاد <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" name="dateOfBirth" required
                           value="${student?.dateOfBirth ? new Date(student.dateOfBirth).toISOString().split('T')[0] : ''}">
                    <div class="invalid-feedback"></div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">الجنس <span class="text-danger">*</span></label>
                    <select class="form-select" name="gender" required>
                      <option value="">اختر الجنس</option>
                      <option value="male" ${student?.gender === 'male' ? 'selected' : ''}>ذكر</option>
                      <option value="female" ${student?.gender === 'female' ? 'selected' : ''}>أنثى</option>
                    </select>
                    <div class="invalid-feedback"></div>
                  </div>

                  <!-- Contact Information -->
                  <div class="col-12 mt-4">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-phone me-2"></i>معلومات الاتصال
                    </h6>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                    <input type="tel" class="form-control" name="phone" required
                           value="${student?.phone || ''}" placeholder="+966xxxxxxxxx">
                    <div class="invalid-feedback"></div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" class="form-control" name="email"
                           value="${student?.email || ''}" placeholder="<EMAIL>">
                    <div class="invalid-feedback"></div>
                  </div>

                  <!-- Address -->
                  <div class="col-12 mt-4">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-map-marker-alt me-2"></i>العنوان
                    </h6>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">الشارع</label>
                    <input type="text" class="form-control" name="address.street"
                           value="${student?.address?.street || ''}">
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">المدينة</label>
                    <input type="text" class="form-control" name="address.city"
                           value="${student?.address?.city || ''}">
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">المنطقة</label>
                    <input type="text" class="form-control" name="address.state"
                           value="${student?.address?.state || ''}">
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">الرمز البريدي</label>
                    <input type="text" class="form-control" name="address.zipCode"
                           value="${student?.address?.zipCode || ''}">
                  </div>

                  <!-- Emergency Contact -->
                  <div class="col-12 mt-4">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-exclamation-triangle me-2"></i>جهة الاتصال في الطوارئ
                    </h6>
                  </div>

                  <div class="col-md-4 mb-3">
                    <label class="form-label">الاسم <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="emergencyContact.name" required
                           value="${student?.emergencyContact?.name || ''}">
                    <div class="invalid-feedback"></div>
                  </div>

                  <div class="col-md-4 mb-3">
                    <label class="form-label">صلة القرابة <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="emergencyContact.relationship" required
                           value="${student?.emergencyContact?.relationship || ''}" placeholder="والد، والدة، أخ...">
                    <div class="invalid-feedback"></div>
                  </div>

                  <div class="col-md-4 mb-3">
                    <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                    <input type="tel" class="form-control" name="emergencyContact.phone" required
                           value="${student?.emergencyContact?.phone || ''}" placeholder="+966xxxxxxxxx">
                    <div class="invalid-feedback"></div>
                  </div>

                  <!-- Taekwondo Information -->
                  <div class="col-12 mt-4">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-medal me-2"></i>معلومات التايكوندو
                    </h6>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">مستوى الحزام</label>
                    <select class="form-select" name="beltLevel">
                      <option value="أبيض" ${student?.beltLevel === 'أبيض' ? 'selected' : ''}>أبيض</option>
                      <option value="أصفر" ${student?.beltLevel === 'أصفر' ? 'selected' : ''}>أصفر</option>
                      <option value="برتقالي" ${student?.beltLevel === 'برتقالي' ? 'selected' : ''}>برتقالي</option>
                      <option value="أخضر" ${student?.beltLevel === 'أخضر' ? 'selected' : ''}>أخضر</option>
                      <option value="أزرق" ${student?.beltLevel === 'أزرق' ? 'selected' : ''}>أزرق</option>
                      <option value="بني" ${student?.beltLevel === 'بني' ? 'selected' : ''}>بني</option>
                      <option value="أسود" ${student?.beltLevel === 'أسود' ? 'selected' : ''}>أسود</option>
                    </select>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                      <option value="active" ${student?.status === 'active' ? 'selected' : ''}>نشط</option>
                      <option value="inactive" ${student?.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                      <option value="suspended" ${student?.status === 'suspended' ? 'selected' : ''}>موقوف</option>
                      <option value="graduated" ${student?.status === 'graduated' ? 'selected' : ''}>متخرج</option>
                    </select>
                  </div>

                  <div class="col-12 mb-3">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-control" name="notes" rows="3"
                              placeholder="أي ملاحظات إضافية...">${student?.notes || ''}</textarea>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-primary" id="saveStudentBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                ${isEdit ? 'تحديث' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('studentModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('studentModal'));
    modal.show();

    // Bind form events
    this.bindStudentFormEvents(isEdit, student);
  }

  bindStudentFormEvents(isEdit, student) {
    const form = document.getElementById('studentForm');
    const saveBtn = document.getElementById('saveStudentBtn');

    if (!form || !saveBtn) return;

    // Form validation
    form.addEventListener('input', (e) => {
      this.validateField(e.target);
    });

    // Save button click
    saveBtn.addEventListener('click', async () => {
      if (this.validateForm(form)) {
        await this.saveStudent(form, isEdit, student);
      }
    });

    // Form submit
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      if (this.validateForm(form)) {
        await this.saveStudent(form, isEdit, student);
      }
    });
  }

  validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      message = 'هذا الحقل مطلوب';
    }

    // Email validation
    if (field.type === 'email' && value && !this.isValidEmail(value)) {
      isValid = false;
      message = 'البريد الإلكتروني غير صحيح';
    }

    // Phone validation
    if (field.type === 'tel' && value && !this.isValidPhone(value)) {
      isValid = false;
      message = 'رقم الهاتف غير صحيح';
    }

    // Date validation
    if (field.type === 'date' && value) {
      const date = new Date(value);
      const today = new Date();
      const age = today.getFullYear() - date.getFullYear();

      if (age < 3 || age > 100) {
        isValid = false;
        message = 'العمر يجب أن يكون بين 3 و 100 سنة';
      }
    }

    // Update field state
    if (isValid) {
      field.classList.remove('is-invalid');
      field.classList.add('is-valid');
    } else {
      field.classList.remove('is-valid');
      field.classList.add('is-invalid');
      const feedback = field.nextElementSibling;
      if (feedback && feedback.classList.contains('invalid-feedback')) {
        feedback.textContent = message;
      }
    }

    return isValid;
  }

  validateForm(form) {
    const fields = form.querySelectorAll('input[required], select[required]');
    let isValid = true;

    fields.forEach(field => {
      if (!this.validateField(field)) {
        isValid = false;
      }
    });

    return isValid;
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidPhone(phone) {
    const phoneRegex = /^[0-9+\-\s()]+$/;
    return phoneRegex.test(phone) && phone.length >= 10;
  }

  async saveStudent(form, isEdit, existingStudent) {
    const saveBtn = document.getElementById('saveStudentBtn');
    const spinner = saveBtn.querySelector('.spinner-border');

    try {
      // Show loading
      saveBtn.disabled = true;
      spinner?.classList.remove('d-none');

      // Collect form data
      const formData = new FormData(form);
      const studentData = {};

      // Process form data
      for (let [key, value] of formData.entries()) {
        if (key.includes('.')) {
          // Handle nested objects (address, emergencyContact)
          const [parent, child] = key.split('.');
          if (!studentData[parent]) studentData[parent] = {};
          studentData[parent][child] = value;
        } else {
          studentData[key] = value;
        }
      }

      // API call
      let response;
      if (isEdit) {
        response = await this.api.students.update(existingStudent._id, studentData);
      } else {
        response = await this.api.students.create(studentData);
      }

      if (response.success) {
        this.utils.showAlert(
          isEdit ? 'تم تحديث بيانات الطالب بنجاح' : 'تم إضافة الطالب بنجاح',
          'success'
        );

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('studentModal'));
        modal.hide();

        // Reload data
        await this.loadStudents();
        await this.loadStatistics();
      } else {
        this.utils.showAlert(response.message || 'حدث خطأ أثناء الحفظ', 'error');
      }
    } catch (error) {
      console.error('Error saving student:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      // Hide loading
      saveBtn.disabled = false;
      spinner?.classList.add('d-none');
    }
  }

  async viewStudent(studentId) {
    try {
      const response = await this.api.students.getById(studentId);
      if (response.success) {
        this.showStudentDetailsModal(response.data.student);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الطالب', 'error');
      }
    } catch (error) {
      console.error('Error loading student:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  }

  async editStudent(studentId) {
    try {
      const response = await this.api.students.getById(studentId);
      if (response.success) {
        this.showStudentModal(response.data.student);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الطالب', 'error');
      }
    } catch (error) {
      console.error('Error loading student:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  }

  async deleteStudent(studentId) {
    const result = await this.utils.showConfirm(
      'تأكيد الحذف',
      'هل أنت متأكد من حذف هذا الطالب؟ سيتم تغيير حالته إلى غير نشط.',
      'حذف',
      'إلغاء'
    );

    if (result) {
      try {
        const response = await this.api.students.delete(studentId);
        if (response.success) {
          this.utils.showAlert('تم حذف الطالب بنجاح', 'success');
          await this.loadStudents();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || 'خطأ في حذف الطالب', 'error');
        }
      } catch (error) {
        console.error('Error deleting student:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      }
    }
  }

  async updateBelt(studentId) {
    try {
      const response = await this.api.students.getById(studentId);
      if (!response.success) {
        this.utils.showAlert('خطأ في تحميل بيانات الطالب', 'error');
        return;
      }

      const student = response.data.student;
      this.showBeltUpdateModal(student);
    } catch (error) {
      console.error('Error loading student:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  }

  showBeltUpdateModal(student) {
    const modalHTML = `
      <div class="modal fade" id="beltModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">تحديث مستوى الحزام</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="text-center mb-4">
                <div class="avatar-lg bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                  <i class="fas fa-medal fa-2x text-warning"></i>
                </div>
                <h6>${student.fullName}</h6>
                <p class="text-muted">الحزام الحالي: <span class="badge ${this.getBeltColor(student.beltLevel)}">${student.beltLevel}</span></p>
              </div>

              <form id="beltForm">
                <div class="mb-3">
                  <label class="form-label">المستوى الجديد <span class="text-danger">*</span></label>
                  <select class="form-select" name="beltLevel" required>
                    <option value="">اختر المستوى الجديد</option>
                    <option value="أبيض">أبيض</option>
                    <option value="أصفر">أصفر</option>
                    <option value="برتقالي">برتقالي</option>
                    <option value="أخضر">أخضر</option>
                    <option value="أزرق">أزرق</option>
                    <option value="بني">بني</option>
                    <option value="أسود">أسود</option>
                  </select>
                  <div class="invalid-feedback">يرجى اختيار المستوى الجديد</div>
                </div>

                <div class="mb-3">
                  <label class="form-label">درجة الاختبار</label>
                  <input type="number" class="form-control" name="testScore" min="0" max="100" placeholder="0-100">
                  <div class="form-text">اختياري - درجة اختبار الحزام</div>
                </div>

                <div class="mb-3">
                  <label class="form-label">ملاحظات</label>
                  <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات حول الترقية..."></textarea>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-primary" id="saveBeltBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                تحديث الحزام
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('beltModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('beltModal'));
    modal.show();

    // Bind events
    const form = document.getElementById('beltForm');
    const saveBtn = document.getElementById('saveBeltBtn');

    saveBtn.addEventListener('click', async () => {
      const formData = new FormData(form);
      const beltLevel = formData.get('beltLevel');

      if (!beltLevel) {
        form.querySelector('[name="beltLevel"]').classList.add('is-invalid');
        return;
      }

      try {
        saveBtn.disabled = true;
        saveBtn.querySelector('.spinner-border')?.classList.remove('d-none');

        const updateData = {
          beltLevel,
          testScore: formData.get('testScore') ? parseInt(formData.get('testScore')) : null,
          notes: formData.get('notes')
        };

        const response = await this.api.students.updateBelt(student._id, updateData);

        if (response.success) {
          this.utils.showAlert('تم تحديث مستوى الحزام بنجاح', 'success');
          modal.hide();
          await this.loadStudents();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || 'خطأ في تحديث الحزام', 'error');
        }
      } catch (error) {
        console.error('Error updating belt:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      } finally {
        saveBtn.disabled = false;
        saveBtn.querySelector('.spinner-border')?.classList.add('d-none');
      }
    });
  }

  showStudentDetailsModal(student) {
    const modalHTML = `
      <div class="modal fade" id="studentDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">تفاصيل الطالب</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                <!-- Student Header -->
                <div class="col-12 mb-4">
                  <div class="text-center">
                    <div class="avatar-xl bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                      <i class="fas fa-user fa-3x text-muted"></i>
                    </div>
                    <h4>${student.fullName}</h4>
                    <p class="text-muted mb-2">رقم الطالب: ${student.studentId}</p>
                    <div class="d-flex justify-content-center gap-2">
                      <span class="badge ${this.getBeltColor(student.beltLevel)}">${student.beltLevel}</span>
                      <span class="badge ${this.getStatusColor(student.status)}">${this.getStatusText(student.status)}</span>
                    </div>
                  </div>
                </div>

                <!-- Personal Information -->
                <div class="col-md-6">
                  <h6 class="text-primary mb-3">المعلومات الشخصية</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">العمر:</td>
                      <td>${student.age} سنة</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">تاريخ الميلاد:</td>
                      <td>${this.utils.formatDate(student.dateOfBirth)}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">الجنس:</td>
                      <td>${student.gender === 'male' ? 'ذكر' : 'أنثى'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">تاريخ التسجيل:</td>
                      <td>${this.utils.formatDate(student.enrollmentDate)}</td>
                    </tr>
                  </table>
                </div>

                <!-- Contact Information -->
                <div class="col-md-6">
                  <h6 class="text-primary mb-3">معلومات الاتصال</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">الهاتف:</td>
                      <td><a href="tel:${student.phone}" class="text-decoration-none">${student.phone}</a></td>
                    </tr>
                    <tr>
                      <td class="fw-medium">البريد الإلكتروني:</td>
                      <td>${student.email ? `<a href="mailto:${student.email}" class="text-decoration-none">${student.email}</a>` : 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">العنوان:</td>
                      <td>${this.formatAddress(student.address)}</td>
                    </tr>
                  </table>
                </div>

                <!-- Emergency Contact -->
                <div class="col-md-6 mt-3">
                  <h6 class="text-primary mb-3">جهة الاتصال في الطوارئ</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">الاسم:</td>
                      <td>${student.emergencyContact?.name || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">صلة القرابة:</td>
                      <td>${student.emergencyContact?.relationship || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">الهاتف:</td>
                      <td>${student.emergencyContact?.phone ? `<a href="tel:${student.emergencyContact.phone}" class="text-decoration-none">${student.emergencyContact.phone}</a>` : 'غير محدد'}</td>
                    </tr>
                  </table>
                </div>

                <!-- Belt History -->
                <div class="col-md-6 mt-3">
                  <h6 class="text-primary mb-3">تاريخ الأحزمة</h6>
                  ${student.beltHistory && student.beltHistory.length > 0 ? `
                    <div class="timeline-sm">
                      ${student.beltHistory.slice(-3).reverse().map(belt => `
                        <div class="timeline-item">
                          <div class="timeline-marker bg-primary"></div>
                          <div class="timeline-content">
                            <div class="fw-medium">${belt.level}</div>
                            <small class="text-muted">${this.utils.formatDate(belt.dateAwarded)}</small>
                            ${belt.testScore ? `<div><small>الدرجة: ${belt.testScore}</small></div>` : ''}
                          </div>
                        </div>
                      `).join('')}
                    </div>
                  ` : '<p class="text-muted">لا يوجد تاريخ أحزمة</p>'}
                </div>

                <!-- Notes -->
                ${student.notes ? `
                  <div class="col-12 mt-3">
                    <h6 class="text-primary mb-3">ملاحظات</h6>
                    <div class="bg-light p-3 rounded">
                      ${student.notes}
                    </div>
                  </div>
                ` : ''}
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
              <button type="button" class="btn btn-warning" onclick="window.studentsPage.editStudent('${student._id}')">
                <i class="fas fa-edit me-2"></i>تعديل
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('studentDetailsModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('studentDetailsModal'));
    modal.show();
  }

  formatAddress(address) {
    if (!address) return 'غير محدد';

    const parts = [
      address.street,
      address.city,
      address.state,
      address.zipCode
    ].filter(part => part && part.trim());

    return parts.length > 0 ? parts.join(', ') : 'غير محدد';
  }

  async exportStudents() {
    try {
      const params = {
        search: this.searchQuery,
        ...this.filters
      };

      // Get all students for export
      const response = await this.api.students.getAll({ ...params, limit: 1000 });

      if (!response.success) {
        this.utils.showAlert('خطأ في تصدير البيانات', 'error');
        return;
      }

      const students = response.data.students;

      // Prepare CSV data
      const csvData = [
        ['رقم الطالب', 'الاسم الكامل', 'العمر', 'الجنس', 'الهاتف', 'البريد الإلكتروني', 'مستوى الحزام', 'الحالة', 'تاريخ التسجيل']
      ];

      students.forEach(student => {
        csvData.push([
          student.studentId,
          student.fullName,
          student.age,
          student.gender === 'male' ? 'ذكر' : 'أنثى',
          student.phone,
          student.email || '',
          student.beltLevel,
          this.getStatusText(student.status),
          this.utils.formatDate(student.enrollmentDate)
        ]);
      });

      // Convert to CSV and download
      const csv = csvData.map(row => row.join(',')).join('\n');
      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `students_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.utils.showAlert('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
      console.error('Error exporting students:', error);
      this.utils.showAlert('خطأ في تصدير البيانات', 'error');
    }
  }
}

// Export for global access
window.StudentsPage = StudentsPage;