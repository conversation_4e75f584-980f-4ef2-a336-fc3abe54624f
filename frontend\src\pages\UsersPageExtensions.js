// UsersPage Extensions - CRUD operations and modals
// This file extends the UsersPage class with additional functionality

// Extend UsersPage prototype with additional methods
Object.assign(UsersPage.prototype, {
  
  // CRUD Operations
  async showUserModal(user = null) {
    const isEdit = !!user;
    const modalTitle = isEdit ? 'تعديل المستخدم' : 'إضافة مستخدم جديد';
    
    const modalHTML = `
      <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${modalTitle}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="userForm" novalidate>
                <div class="row">
                  <!-- Basic Information -->
                  <div class="col-12 mb-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-user me-2"></i>المعلومات الأساسية
                    </h6>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="fullName" required maxlength="100"
                           value="${user?.fullName || ''}" placeholder="الاسم الكامل للمستخدم">
                    <div class="invalid-feedback">الاسم الكامل مطلوب</div>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="username" required minlength="3" maxlength="30"
                           value="${user?.username || ''}" placeholder="اسم المستخدم للدخول" ${isEdit ? 'readonly' : ''}>
                    <div class="invalid-feedback">اسم المستخدم مطلوب (3-30 حرف)</div>
                    ${isEdit ? '<div class="form-text">لا يمكن تغيير اسم المستخدم</div>' : ''}
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" name="email" required
                           value="${user?.email || ''}" placeholder="<EMAIL>">
                    <div class="invalid-feedback">البريد الإلكتروني مطلوب وصحيح</div>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" name="phone"
                           value="${user?.phone || ''}" placeholder="+966 50 123 4567">
                    <div class="invalid-feedback">رقم الهاتف غير صحيح</div>
                  </div>
                  
                  ${!isEdit ? `
                    <div class="col-md-6 mb-3">
                      <label class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                      <input type="password" class="form-control" name="password" required minlength="6"
                             placeholder="كلمة المرور (6 أحرف على الأقل)">
                      <div class="invalid-feedback">كلمة المرور مطلوبة (6 أحرف على الأقل)</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                      <input type="password" class="form-control" name="confirmPassword" required minlength="6"
                             placeholder="تأكيد كلمة المرور">
                      <div class="invalid-feedback">تأكيد كلمة المرور مطلوب</div>
                    </div>
                  ` : ''}
                  
                  <!-- Role and Status -->
                  <div class="col-12 mt-3 mb-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-shield-alt me-2"></i>الدور والحالة
                    </h6>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">الدور <span class="text-danger">*</span></label>
                    <select class="form-select" name="role" required>
                      <option value="">اختر الدور</option>
                      <option value="admin" ${user?.role === 'admin' ? 'selected' : ''}>مدير النظام</option>
                      <option value="coach" ${user?.role === 'coach' ? 'selected' : ''}>مدرب</option>
                      <option value="receptionist" ${user?.role === 'receptionist' ? 'selected' : ''}>موظف استقبال</option>
                    </select>
                    <div class="invalid-feedback">الدور مطلوب</div>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="isActive">
                      <option value="true" ${user?.isActive !== false ? 'selected' : ''}>نشط</option>
                      <option value="false" ${user?.isActive === false ? 'selected' : ''}>غير نشط</option>
                    </select>
                  </div>
                  
                  <!-- Permissions -->
                  <div class="col-12 mt-3 mb-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-key me-2"></i>الصلاحيات
                    </h6>
                    <div class="alert alert-info">
                      <i class="fas fa-info-circle me-2"></i>
                      مدراء النظام لديهم جميع الصلاحيات تلقائياً
                    </div>
                  </div>
                  
                  <div class="col-12 mb-3">
                    <div class="row" id="permissionsContainer">
                      ${Object.entries(this.permissions).map(([key, label]) => `
                        <div class="col-md-6 mb-2">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="permissions" 
                                   value="${key}" id="perm_${key}"
                                   ${user?.permissions?.includes(key) ? 'checked' : ''}>
                            <label class="form-check-label" for="perm_${key}">
                              ${label}
                            </label>
                          </div>
                        </div>
                      `).join('')}
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-primary" id="saveUserBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                ${isEdit ? 'تحديث' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('userModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();

    // Bind form events
    this.bindUserFormEvents(isEdit, user);
  },

  bindUserFormEvents(isEdit, user) {
    const form = document.getElementById('userForm');
    const saveBtn = document.getElementById('saveUserBtn');
    const roleSelect = form.querySelector('[name="role"]');
    const permissionsContainer = document.getElementById('permissionsContainer');
    
    if (!form || !saveBtn) return;

    // Role change handler - disable permissions for admin
    roleSelect.addEventListener('change', (e) => {
      const isAdmin = e.target.value === 'admin';
      const checkboxes = permissionsContainer.querySelectorAll('input[type="checkbox"]');
      
      checkboxes.forEach(checkbox => {
        checkbox.disabled = isAdmin;
        if (isAdmin) {
          checkbox.checked = true;
        }
      });
    });

    // Password confirmation validation
    if (!isEdit) {
      const passwordInput = form.querySelector('[name="password"]');
      const confirmPasswordInput = form.querySelector('[name="confirmPassword"]');
      
      confirmPasswordInput.addEventListener('input', () => {
        if (passwordInput.value !== confirmPasswordInput.value) {
          confirmPasswordInput.setCustomValidity('كلمات المرور غير متطابقة');
        } else {
          confirmPasswordInput.setCustomValidity('');
        }
      });
    }

    // Form validation
    form.addEventListener('input', (e) => {
      this.validateUserField(e.target);
    });

    // Save button click
    saveBtn.addEventListener('click', async () => {
      if (this.validateUserForm(form)) {
        await this.saveUser(form, isEdit, user);
      }
    });

    // Form submit
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      if (this.validateUserForm(form)) {
        await this.saveUser(form, isEdit, user);
      }
    });

    // Initial role check
    if (roleSelect.value === 'admin') {
      roleSelect.dispatchEvent(new Event('change'));
    }
  },

  validateUserField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      message = 'هذا الحقل مطلوب';
    }

    // Email validation
    if (field.type === 'email' && value) {
      const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
      if (!emailRegex.test(value)) {
        isValid = false;
        message = 'البريد الإلكتروني غير صحيح';
      }
    }

    // Phone validation
    if (field.name === 'phone' && value) {
      const phoneRegex = /^[0-9+\-\s()]+$/;
      if (!phoneRegex.test(value)) {
        isValid = false;
        message = 'رقم الهاتف غير صحيح';
      }
    }

    // Length validation
    if (field.minLength && value.length < field.minLength) {
      isValid = false;
      message = `يجب أن يكون ${field.minLength} أحرف على الأقل`;
    }

    if (field.maxLength && value.length > field.maxLength) {
      isValid = false;
      message = `يجب أن يكون ${field.maxLength} حرف أو أقل`;
    }

    // Update field state
    if (isValid) {
      field.classList.remove('is-invalid');
      field.classList.add('is-valid');
    } else {
      field.classList.remove('is-valid');
      field.classList.add('is-invalid');
      const feedback = field.nextElementSibling;
      if (feedback && feedback.classList.contains('invalid-feedback')) {
        feedback.textContent = message;
      }
    }

    return isValid;
  },

  validateUserForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!this.validateUserField(field)) {
        isValid = false;
      }
    });

    // Additional password confirmation validation
    const passwordInput = form.querySelector('[name="password"]');
    const confirmPasswordInput = form.querySelector('[name="confirmPassword"]');
    
    if (passwordInput && confirmPasswordInput) {
      if (passwordInput.value !== confirmPasswordInput.value) {
        confirmPasswordInput.classList.add('is-invalid');
        const feedback = confirmPasswordInput.nextElementSibling;
        if (feedback && feedback.classList.contains('invalid-feedback')) {
          feedback.textContent = 'كلمات المرور غير متطابقة';
        }
        isValid = false;
      }
    }

    return isValid;
  }
});
