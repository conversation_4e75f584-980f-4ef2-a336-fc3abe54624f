// ClassesPage Extensions 2 - Additional CRUD and utility methods
// This file extends the ClassesPage class with more functionality

// Extend ClassesPage prototype with additional methods
Object.assign(ClassesPage.prototype, {
  
  async viewClass(classId) {
    try {
      const response = await this.api.classes.getById(classId);
      if (response.success) {
        this.showClassDetailsModal(response.data.class);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الصف', 'error');
      }
    } catch (error) {
      console.error('Error loading class:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  async editClass(classId) {
    try {
      const response = await this.api.classes.getById(classId);
      if (response.success) {
        this.showClassModal(response.data.class);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الصف', 'error');
      }
    } catch (error) {
      console.error('Error loading class:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  async deleteClass(classId) {
    const result = await this.utils.showConfirm(
      'تأكيد الحذف',
      'هل أنت متأكد من حذف هذا الصف؟ سيتم إلغاء تسجيل جميع الطلاب المسجلين.',
      'حذف',
      'إلغاء'
    );

    if (result) {
      try {
        const response = await this.api.classes.delete(classId);
        if (response.success) {
          this.utils.showAlert('تم حذف الصف بنجاح', 'success');
          await this.loadClasses();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || 'خطأ في حذف الصف', 'error');
        }
      } catch (error) {
        console.error('Error deleting class:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      }
    }
  },

  async toggleClassStatus(classId) {
    try {
      const response = await this.api.classes.toggleStatus(classId);
      if (response.success) {
        this.utils.showAlert('تم تغيير حالة الصف بنجاح', 'success');
        await this.loadClasses();
        await this.loadStatistics();
      } else {
        this.utils.showAlert(response.message || 'خطأ في تغيير حالة الصف', 'error');
      }
    } catch (error) {
      console.error('Error toggling class status:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  async manageStudents(classId) {
    try {
      const response = await this.api.classes.getById(classId);
      if (response.success) {
        this.showStudentManagementModal(response.data.class);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الصف', 'error');
      }
    } catch (error) {
      console.error('Error loading class:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  showClassDetailsModal(classItem) {
    const modalHTML = `
      <div class="modal fade" id="classDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">تفاصيل الصف</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                <!-- Class Header -->
                <div class="col-12 mb-4">
                  <div class="text-center">
                    <div class="avatar-xl bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                      <i class="fas fa-chalkboard-teacher fa-3x text-primary"></i>
                    </div>
                    <h4>${classItem.name}</h4>
                    <div class="d-flex justify-content-center gap-2 mt-2">
                      <span class="badge ${this.getTypeColor(classItem.type)}">${this.getTypeText(classItem.type)}</span>
                      <span class="badge ${this.getLevelColor(classItem.level)}">${this.getLevelText(classItem.level)}</span>
                      <span class="badge ${classItem.isActive ? 'bg-success' : 'bg-secondary'}">${classItem.isActive ? 'نشط' : 'غير نشط'}</span>
                    </div>
                  </div>
                </div>
                
                <!-- Basic Info -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">المعلومات الأساسية</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">اسم الصف:</td>
                      <td>${classItem.name}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">النوع:</td>
                      <td><span class="badge ${this.getTypeColor(classItem.type)}">${this.getTypeText(classItem.type)}</span></td>
                    </tr>
                    <tr>
                      <td class="fw-medium">المستوى:</td>
                      <td><span class="badge ${this.getLevelColor(classItem.level)}">${this.getLevelText(classItem.level)}</span></td>
                    </tr>
                    <tr>
                      <td class="fw-medium">السعة:</td>
                      <td>${classItem.capacity} طالب</td>
                    </tr>
                  </table>
                </div>
                
                <!-- Schedule -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">الجدولة</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">اليوم:</td>
                      <td>${this.daysOfWeek[classItem.schedule.dayOfWeek]}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">وقت البداية:</td>
                      <td>${classItem.schedule.startTime}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">وقت النهاية:</td>
                      <td>${classItem.schedule.endTime}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">المدة:</td>
                      <td>${this.calculateDuration(classItem.schedule.startTime, classItem.schedule.endTime)} دقيقة</td>
                    </tr>
                  </table>
                </div>
                
                <!-- Instructors -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">المدربون</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">المدرب الرئيسي:</td>
                      <td>${classItem.instructor?.fullName || 'غير محدد'}</td>
                    </tr>
                    ${classItem.assistantInstructor ? `
                      <tr>
                        <td class="fw-medium">المدرب المساعد:</td>
                        <td>${classItem.assistantInstructor.fullName}</td>
                      </tr>
                    ` : ''}
                  </table>
                </div>
                
                <!-- Enrollment -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">التسجيل</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">المسجلون حالياً:</td>
                      <td>${classItem.currentEnrollment}/${classItem.capacity}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">الأماكن المتاحة:</td>
                      <td>${classItem.availableSpots}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">نسبة الإشغال:</td>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="progress me-2" style="width: 100px; height: 8px;">
                            <div class="progress-bar ${this.getOccupancyColor(classItem.currentEnrollment, classItem.capacity)}" 
                                 style="width: ${(classItem.currentEnrollment / classItem.capacity) * 100}%"></div>
                          </div>
                          <small>${Math.round((classItem.currentEnrollment / classItem.capacity) * 100)}%</small>
                        </div>
                      </td>
                    </tr>
                  </table>
                </div>
                
                <!-- Age Range & Fees -->
                ${classItem.ageRange?.min || classItem.ageRange?.max || classItem.fees?.monthly || classItem.fees?.perSession ? `
                  <div class="col-md-6 mb-4">
                    <h6 class="text-primary mb-3">الفئة العمرية والرسوم</h6>
                    <table class="table table-sm">
                      ${classItem.ageRange?.min || classItem.ageRange?.max ? `
                        <tr>
                          <td class="fw-medium">الفئة العمرية:</td>
                          <td>${classItem.ageRange?.min || 'غير محدد'} - ${classItem.ageRange?.max || 'غير محدد'} سنة</td>
                        </tr>
                      ` : ''}
                      ${classItem.fees?.monthly ? `
                        <tr>
                          <td class="fw-medium">الرسوم الشهرية:</td>
                          <td>${classItem.fees.monthly} ريال</td>
                        </tr>
                      ` : ''}
                      ${classItem.fees?.perSession ? `
                        <tr>
                          <td class="fw-medium">رسوم الجلسة:</td>
                          <td>${classItem.fees.perSession} ريال</td>
                        </tr>
                      ` : ''}
                    </table>
                  </div>
                ` : ''}
                
                <!-- Dates -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">التواريخ</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">تاريخ البداية:</td>
                      <td>${this.utils.formatDate(classItem.startDate)}</td>
                    </tr>
                    ${classItem.endDate ? `
                      <tr>
                        <td class="fw-medium">تاريخ النهاية:</td>
                        <td>${this.utils.formatDate(classItem.endDate)}</td>
                      </tr>
                    ` : `
                      <tr>
                        <td class="fw-medium">النوع:</td>
                        <td>صف مستمر</td>
                      </tr>
                    `}
                  </table>
                </div>
                
                <!-- Description -->
                ${classItem.description ? `
                  <div class="col-12 mb-3">
                    <h6 class="text-primary mb-3">الوصف</h6>
                    <div class="bg-light p-3 rounded">
                      ${classItem.description}
                    </div>
                  </div>
                ` : ''}
                
                <!-- Requirements & Equipment -->
                ${classItem.requirements?.length || classItem.equipment?.length ? `
                  <div class="col-md-6 mb-3">
                    ${classItem.requirements?.length ? `
                      <h6 class="text-primary mb-3">المتطلبات</h6>
                      <ul class="list-unstyled">
                        ${classItem.requirements.map(req => `<li><i class="fas fa-check text-success me-2"></i>${req}</li>`).join('')}
                      </ul>
                    ` : ''}
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    ${classItem.equipment?.length ? `
                      <h6 class="text-primary mb-3">المعدات المطلوبة</h6>
                      <ul class="list-unstyled">
                        ${classItem.equipment.map(eq => `<li><i class="fas fa-tools text-info me-2"></i>${eq}</li>`).join('')}
                      </ul>
                    ` : ''}
                  </div>
                ` : ''}
                
                <!-- Notes -->
                ${classItem.notes ? `
                  <div class="col-12 mb-3">
                    <h6 class="text-primary mb-3">ملاحظات</h6>
                    <div class="bg-light p-3 rounded">
                      ${classItem.notes}
                    </div>
                  </div>
                ` : ''}
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
              <button type="button" class="btn btn-info" onclick="window.classesPage.manageStudents('${classItem._id}')">
                <i class="fas fa-users me-2"></i>إدارة الطلاب
              </button>
              <button type="button" class="btn btn-warning" onclick="window.classesPage.editClass('${classItem._id}')">
                <i class="fas fa-edit me-2"></i>تعديل
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('classDetailsModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('classDetailsModal'));
    modal.show();
  },

  calculateDuration(startTime, endTime) {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    return Math.round((end - start) / (1000 * 60)); // Duration in minutes
  },

  async exportClasses() {
    try {
      const params = {
        search: this.searchQuery,
        ...this.filters
      };

      // Get all classes for export
      const response = await this.api.classes.getAll({ ...params, limit: 1000 });
      
      if (!response.success) {
        this.utils.showAlert('خطأ في تصدير البيانات', 'error');
        return;
      }

      const classes = response.data.classes;
      
      // Prepare CSV data
      const csvData = [
        ['اسم الصف', 'النوع', 'المستوى', 'المدرب', 'اليوم', 'وقت البداية', 'وقت النهاية', 'السعة', 'المسجلون', 'الرسوم الشهرية', 'الحالة']
      ];

      classes.forEach(classItem => {
        csvData.push([
          classItem.name,
          this.getTypeText(classItem.type),
          this.getLevelText(classItem.level),
          classItem.instructor?.fullName || '',
          this.daysOfWeek[classItem.schedule.dayOfWeek],
          classItem.schedule.startTime,
          classItem.schedule.endTime,
          classItem.capacity,
          classItem.currentEnrollment,
          classItem.fees?.monthly || '',
          classItem.isActive ? 'نشط' : 'غير نشط'
        ]);
      });

      // Convert to CSV and download
      const csv = csvData.map(row => row.join(',')).join('\n');
      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      
      link.setAttribute('href', url);
      link.setAttribute('download', `classes_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      this.utils.showAlert('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
      console.error('Error exporting classes:', error);
      this.utils.showAlert('خطأ في تصدير البيانات', 'error');
    }
  },

  async showScheduleView() {
    // This would show a weekly schedule view
    // For now, we'll show a simple alert
    this.utils.showAlert('عرض الجدول الأسبوعي قيد التطوير', 'info');
  }
});
