// PaymentsPage Extensions - Additional methods for PaymentsPage class
// This file extends the PaymentsPage class with additional functionality

// Extend PaymentsPage prototype with additional methods
Object.assign(PaymentsPage.prototype, {
  
  async viewPayment(paymentId) {
    try {
      const response = await this.api.payments.getById(paymentId);
      if (response.success) {
        this.showPaymentDetailsModal(response.data.payment);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الدفعة', 'error');
      }
    } catch (error) {
      console.error('Error loading payment:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  async deletePayment(paymentId) {
    const result = await this.utils.showConfirm(
      'تأكيد الحذف',
      'هل أنت متأكد من حذف هذه الدفعة؟ هذا الإجراء لا يمكن التراجع عنه.',
      'حذف',
      'إلغاء'
    );

    if (result) {
      try {
        const response = await this.api.payments.delete(paymentId);
        if (response.success) {
          this.utils.showAlert('تم حذف الدفعة بنجاح', 'success');
          await this.loadPayments();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || 'خطأ في حذف الدفعة', 'error');
        }
      } catch (error) {
        console.error('Error deleting payment:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      }
    }
  },

  async processRefund(paymentId) {
    try {
      const response = await this.api.payments.getById(paymentId);
      if (!response.success) {
        this.utils.showAlert('خطأ في تحميل بيانات الدفعة', 'error');
        return;
      }

      const payment = response.data.payment;
      this.showRefundModal(payment);
    } catch (error) {
      console.error('Error loading payment:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  showRefundModal(payment) {
    const modalHTML = `
      <div class="modal fade" id="refundModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">استرداد الدفعة</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="text-center mb-4">
                <div class="avatar-lg bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                  <i class="fas fa-undo fa-2x text-warning"></i>
                </div>
                <h6>استرداد دفعة</h6>
                <p class="text-muted">${payment.paymentId}</p>
              </div>
              
              <div class="alert alert-info">
                <strong>المبلغ الأصلي:</strong> ${payment.amount} ريال<br>
                <strong>الطالب:</strong> ${payment.student?.fullName}<br>
                <strong>تاريخ الدفع:</strong> ${this.utils.formatDate(payment.paymentDate)}
              </div>
              
              <form id="refundForm">
                <div class="mb-3">
                  <label class="form-label">مبلغ الاسترداد (ريال) <span class="text-danger">*</span></label>
                  <input type="number" class="form-control" name="amount" required 
                         min="0" max="${payment.amount}" step="0.01" value="${payment.amount}">
                  <div class="form-text">الحد الأقصى: ${payment.amount} ريال</div>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">سبب الاسترداد <span class="text-danger">*</span></label>
                  <select class="form-select" name="reason" required>
                    <option value="">اختر السبب</option>
                    <option value="customer_request">طلب العميل</option>
                    <option value="service_issue">مشكلة في الخدمة</option>
                    <option value="duplicate_payment">دفعة مكررة</option>
                    <option value="cancellation">إلغاء الاشتراك</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">ملاحظات</label>
                  <textarea class="form-control" name="notes" rows="3" 
                            placeholder="تفاصيل إضافية حول سبب الاسترداد..."></textarea>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-warning" id="confirmRefundBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                تأكيد الاسترداد
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('refundModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('refundModal'));
    modal.show();

    // Bind refund events
    this.bindRefundEvents(payment);
  },

  bindRefundEvents(payment) {
    const form = document.getElementById('refundForm');
    const confirmBtn = document.getElementById('confirmRefundBtn');

    // Confirm refund
    confirmBtn.addEventListener('click', async () => {
      const formData = new FormData(form);
      const amount = parseFloat(formData.get('amount'));
      const reason = formData.get('reason');
      const notes = formData.get('notes');

      if (!amount || !reason) {
        this.utils.showAlert('يرجى تحديد مبلغ الاسترداد والسبب', 'error');
        return;
      }

      if (amount > payment.amount) {
        this.utils.showAlert('مبلغ الاسترداد لا يمكن أن يكون أكبر من المبلغ الأصلي', 'error');
        return;
      }

      try {
        confirmBtn.disabled = true;
        confirmBtn.querySelector('.spinner-border')?.classList.remove('d-none');

        const refundData = { amount, reason, notes };
        const response = await this.api.payments.refund(payment._id, refundData);
        
        if (response.success) {
          this.utils.showAlert('تم تنفيذ الاسترداد بنجاح', 'success');
          
          // Close modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('refundModal'));
          modal.hide();
          
          // Reload data
          await this.loadPayments();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || 'خطأ في تنفيذ الاسترداد', 'error');
        }
      } catch (error) {
        console.error('Error processing refund:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      } finally {
        confirmBtn.disabled = false;
        confirmBtn.querySelector('.spinner-border')?.classList.add('d-none');
      }
    });
  },

  async printReceipt(paymentId) {
    try {
      const response = await this.api.payments.getById(paymentId);
      if (response.success) {
        const payment = response.data.payment;
        this.generateReceipt(payment);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الدفعة', 'error');
      }
    } catch (error) {
      console.error('Error loading payment for receipt:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  },

  generateReceipt(payment) {
    const receiptHTML = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>إيصال دفع - ${payment.receiptNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .receipt { max-width: 400px; margin: 0 auto; border: 1px solid #ddd; padding: 20px; }
          .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 20px; }
          .row { display: flex; justify-content: space-between; margin-bottom: 10px; }
          .total { border-top: 2px solid #000; padding-top: 10px; font-weight: bold; }
          @media print { body { margin: 0; } .receipt { border: none; } }
        </style>
      </head>
      <body>
        <div class="receipt">
          <div class="header">
            <h2>أكاديمية التايكوندو</h2>
            <p>إيصال دفع</p>
          </div>
          
          <div class="row">
            <span>رقم الإيصال:</span>
            <span>${payment.receiptNumber}</span>
          </div>
          
          <div class="row">
            <span>التاريخ:</span>
            <span>${this.utils.formatDate(payment.paymentDate)}</span>
          </div>
          
          <div class="row">
            <span>الطالب:</span>
            <span>${payment.student?.fullName}</span>
          </div>
          
          <div class="row">
            <span>رقم الطالب:</span>
            <span>${payment.student?.studentId}</span>
          </div>
          
          <div class="row">
            <span>الاشتراك:</span>
            <span>${payment.subscription?.subscriptionId}</span>
          </div>
          
          <div class="row">
            <span>طريقة الدفع:</span>
            <span>${this.getPaymentMethodText(payment.paymentMethod)}</span>
          </div>
          
          ${payment.transactionId ? `
            <div class="row">
              <span>رقم المعاملة:</span>
              <span>${payment.transactionId}</span>
            </div>
          ` : ''}
          
          <div class="row total">
            <span>المبلغ المدفوع:</span>
            <span>${payment.amount} ريال</span>
          </div>
          
          ${payment.notes ? `
            <div style="margin-top: 20px;">
              <strong>ملاحظات:</strong><br>
              ${payment.notes}
            </div>
          ` : ''}
          
          <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #666;">
            شكراً لكم لاختياركم أكاديمية التايكوندو
          </div>
        </div>
        
        <script>
          window.onload = function() {
            window.print();
            window.onafterprint = function() {
              window.close();
            };
          };
        </script>
      </body>
      </html>
    `;

    // Open receipt in new window for printing
    const receiptWindow = window.open('', '_blank', 'width=600,height=800');
    receiptWindow.document.write(receiptHTML);
    receiptWindow.document.close();
  },

  async exportPayments() {
    try {
      const params = {
        search: this.searchQuery,
        ...this.filters
      };

      // Get all payments for export
      const response = await this.api.payments.getAll({ ...params, limit: 1000 });
      
      if (!response.success) {
        this.utils.showAlert('خطأ في تصدير البيانات', 'error');
        return;
      }

      const payments = response.data.payments;
      
      // Prepare CSV data
      const csvData = [
        ['رقم الدفعة', 'الطالب', 'رقم الطالب', 'الاشتراك', 'المبلغ', 'طريقة الدفع', 'تاريخ الدفع', 'رقم الإيصال', 'الحالة', 'ملاحظات']
      ];

      payments.forEach(payment => {
        csvData.push([
          payment.paymentId,
          payment.student?.fullName || '',
          payment.student?.studentId || '',
          payment.subscription?.subscriptionId || '',
          payment.amount,
          this.getPaymentMethodText(payment.paymentMethod),
          this.utils.formatDate(payment.paymentDate),
          payment.receiptNumber || '',
          this.getStatusText(payment.status),
          payment.notes || ''
        ]);
      });

      // Convert to CSV and download
      const csv = csvData.map(row => row.join(',')).join('\n');
      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      
      link.setAttribute('href', url);
      link.setAttribute('download', `payments_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      this.utils.showAlert('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
      console.error('Error exporting payments:', error);
      this.utils.showAlert('خطأ في تصدير البيانات', 'error');
    }
  },

  async showFinancialReport() {
    // This would show a comprehensive financial report
    // For now, we'll show a simple alert
    this.utils.showAlert('التقرير المالي قيد التطوير', 'info');
  },

  showPaymentDetailsModal(payment) {
    const modalHTML = `
      <div class="modal fade" id="paymentDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">تفاصيل الدفعة</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                <!-- Payment Header -->
                <div class="col-12 mb-4">
                  <div class="text-center">
                    <div class="avatar-xl bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                      <i class="fas fa-money-bill-wave fa-3x text-success"></i>
                    </div>
                    <h4>${payment.paymentId}</h4>
                    <div class="d-flex justify-content-center gap-2 mt-2">
                      <span class="badge ${this.getPaymentMethodColor(payment.paymentMethod)}">${this.getPaymentMethodText(payment.paymentMethod)}</span>
                      <span class="badge ${this.getStatusColor(payment.status)}">${this.getStatusText(payment.status)}</span>
                    </div>
                  </div>
                </div>

                <!-- Student Info -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">معلومات الطالب</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">الاسم:</td>
                      <td>${payment.student?.fullName || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">رقم الطالب:</td>
                      <td>${payment.student?.studentId || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">الهاتف:</td>
                      <td>${payment.student?.phone || 'غير محدد'}</td>
                    </tr>
                  </table>
                </div>

                <!-- Subscription Info -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">معلومات الاشتراك</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">رقم الاشتراك:</td>
                      <td>${payment.subscription?.subscriptionId || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">نوع الخطة:</td>
                      <td>${payment.subscription?.plan?.name ? this.getPlanText(payment.subscription.plan.name) : 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">مبلغ الاشتراك:</td>
                      <td>${payment.subscription?.finalAmount || payment.subscription?.amount || 'غير محدد'} ريال</td>
                    </tr>
                  </table>
                </div>

                <!-- Payment Details -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">تفاصيل الدفعة</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">المبلغ:</td>
                      <td class="fw-bold">${payment.amount} ريال</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">تاريخ الدفع:</td>
                      <td>${this.utils.formatDateTime(payment.paymentDate)}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">طريقة الدفع:</td>
                      <td><span class="badge ${this.getPaymentMethodColor(payment.paymentMethod)}">${this.getPaymentMethodText(payment.paymentMethod)}</span></td>
                    </tr>
                    ${payment.transactionId ? `
                      <tr>
                        <td class="fw-medium">رقم المعاملة:</td>
                        <td>${payment.transactionId}</td>
                      </tr>
                    ` : ''}
                  </table>
                </div>

                <!-- Receipt & Status -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">الإيصال والحالة</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">رقم الإيصال:</td>
                      <td>${payment.receiptNumber || 'لم يتم إنشاؤه'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">الحالة:</td>
                      <td><span class="badge ${this.getStatusColor(payment.status)}">${this.getStatusText(payment.status)}</span></td>
                    </tr>
                    <tr>
                      <td class="fw-medium">المبلغ الصافي:</td>
                      <td class="fw-bold">${payment.netAmount || payment.amount} ريال</td>
                    </tr>
                  </table>
                </div>

                <!-- Refund Info -->
                ${payment.refund?.amount > 0 ? `
                  <div class="col-12 mb-4">
                    <h6 class="text-primary mb-3">معلومات الاسترداد</h6>
                    <div class="alert alert-warning">
                      <div class="row">
                        <div class="col-md-3">
                          <strong>مبلغ الاسترداد:</strong><br>
                          ${payment.refund.amount} ريال
                        </div>
                        <div class="col-md-3">
                          <strong>تاريخ الاسترداد:</strong><br>
                          ${this.utils.formatDate(payment.refund.processedAt)}
                        </div>
                        <div class="col-md-3">
                          <strong>السبب:</strong><br>
                          ${payment.refund.reason}
                        </div>
                        <div class="col-md-3">
                          <strong>المعالج:</strong><br>
                          ${payment.refund.processedBy?.fullName || 'غير محدد'}
                        </div>
                      </div>
                      ${payment.refund.notes ? `
                        <div class="mt-2">
                          <strong>ملاحظات:</strong> ${payment.refund.notes}
                        </div>
                      ` : ''}
                    </div>
                  </div>
                ` : ''}

                <!-- Notes -->
                ${payment.notes ? `
                  <div class="col-12 mb-3">
                    <h6 class="text-primary mb-3">ملاحظات</h6>
                    <div class="bg-light p-3 rounded">
                      ${payment.notes}
                    </div>
                  </div>
                ` : ''}

                <!-- Metadata -->
                <div class="col-12">
                  <h6 class="text-primary mb-3">معلومات إضافية</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">سجل بواسطة:</td>
                      <td>${payment.recordedBy?.fullName || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">تاريخ التسجيل:</td>
                      <td>${this.utils.formatDateTime(payment.createdAt)}</td>
                    </tr>
                    ${payment.updatedAt !== payment.createdAt ? `
                      <tr>
                        <td class="fw-medium">آخر تحديث:</td>
                        <td>${this.utils.formatDateTime(payment.updatedAt)}</td>
                      </tr>
                    ` : ''}
                  </table>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
              ${payment.receiptNumber ? `
                <button type="button" class="btn btn-info" onclick="window.paymentsPage.printReceipt('${payment._id}')">
                  <i class="fas fa-print me-2"></i>طباعة الإيصال
                </button>
              ` : ''}
              ${payment.status === 'completed' && !payment.refund?.amount ? `
                <button type="button" class="btn btn-warning" onclick="window.paymentsPage.processRefund('${payment._id}')">
                  <i class="fas fa-undo me-2"></i>استرداد
                </button>
              ` : ''}
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('paymentDetailsModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('paymentDetailsModal'));
    modal.show();
  }
});
