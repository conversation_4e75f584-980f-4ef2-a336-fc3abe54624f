/* Custom styles for Taekwondo CRM */

:root {
  --primary-color: #dc3545;
  --primary-dark: #c82333;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --border-radius: 0.375rem;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --transition: all 0.15s ease-in-out;
}

/* Global styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--light-color);
  line-height: 1.6;
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-lg);
}

/* Toast notifications */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  min-width: 300px;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-lg);
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1050;
  border-left: 4px solid var(--primary-color);
}

.toast.show {
  transform: translateX(0);
}

.toast-content {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toast-success {
  border-left-color: var(--success-color);
}

.toast-error {
  border-left-color: var(--danger-color);
}

.toast-warning {
  border-left-color: var(--warning-color);
}

.toast-info {
  border-left-color: var(--info-color);
}

/* Navigation */
.navbar-brand {
  font-weight: bold;
  color: var(--primary-color) !important;
  font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.85);
  transition: var(--transition);
}

.navbar-dark .navbar-nav .nav-link:hover {
  color: white;
}

/* Cards */
.card {
  border: none;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  border-radius: var(--border-radius);
}

.card:hover {
  box-shadow: var(--box-shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--light-color);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  font-weight: 600;
}

/* Buttons */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Forms */
.form-control {
  border-radius: var(--border-radius);
  border: 1px solid #ced4da;
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-label {
  font-weight: 500;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

.invalid-feedback {
  display: block;
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Tables */
.table {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.table thead th {
  background-color: var(--dark-color);
  color: white;
  font-weight: 600;
  border: none;
  padding: 1rem 0.75rem;
}

.table tbody tr {
  transition: var(--transition);
}

.table tbody tr:hover {
  background-color: rgba(220, 53, 69, 0.05);
}

.table td {
  padding: 0.75rem;
  vertical-align: middle;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Sortable table headers */
.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.sortable:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sortable.sorted-asc::after {
  content: '\f0de';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  right: 0.5rem;
  color: var(--primary-color);
}

.sortable.sorted-desc::after {
  content: '\f0dd';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  right: 0.5rem;
  color: var(--primary-color);
}

/* Badges */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 50rem;
}

/* Dashboard specific styles */
.dashboard-card {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
  transition: var(--transition);
}

.dashboard-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-lg);
}

.dashboard-card .card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.dashboard-card .card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.dashboard-card .card-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0;
}

/* Activity feed */
.activity-item {
  padding: 0.75rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.activity-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(220, 53, 69, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-message {
  font-weight: 500;
  color: var(--dark-color);
}

/* Pagination */
.pagination .page-link {
  color: var(--primary-color);
  border-color: #dee2e6;
  border-radius: var(--border-radius);
  margin: 0 0.125rem;
}

.pagination .page-link:hover {
  color: var(--primary-dark);
  background-color: rgba(220, 53, 69, 0.1);
  border-color: var(--primary-color);
}

.pagination .page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card {
    margin-bottom: 1rem;
  }
  
  .table-responsive {
    border-radius: var(--border-radius);
  }
  
  .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .dashboard-card {
    margin-bottom: 1rem;
  }
  
  .dashboard-card .card-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 576px) {
  .navbar-brand {
    font-size: 1.25rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .btn {
    font-size: 0.875rem;
  }
  
  .table td, .table th {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}

/* Dark theme support */
[data-theme="dark"] {
  --light-color: #212529;
  --dark-color: #f8f9fa;
}

[data-theme="dark"] body {
  background-color: #121212;
  color: #f8f9fa;
}

[data-theme="dark"] .card {
  background-color: #1e1e1e;
  color: #f8f9fa;
}

[data-theme="dark"] .table {
  color: #f8f9fa;
}

[data-theme="dark"] .table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

/* Print styles */
@media print {
  .navbar, .btn, .pagination {
    display: none !important;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #dee2e6;
  }
  
  .table {
    font-size: 0.875rem;
  }
}

/* Students Page Specific Styles */
.students-page .avatar-sm {
  width: 32px;
  height: 32px;
  font-size: 0.75rem;
}

.students-page .avatar-lg {
  width: 64px;
  height: 64px;
  font-size: 1.5rem;
}

.students-page .avatar-xl {
  width: 80px;
  height: 80px;
  font-size: 2rem;
}

.students-page .bg-pink {
  background-color: #e91e63 !important;
}

.students-page .timeline-sm {
  position: relative;
  padding-left: 1.5rem;
}

.students-page .timeline-item {
  position: relative;
  margin-bottom: 1rem;
}

.students-page .timeline-marker {
  position: absolute;
  left: -1.75rem;
  top: 0.25rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.students-page .timeline-content {
  padding-left: 0.5rem;
}

.students-page .timeline-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: -1.71rem;
  top: 1rem;
  width: 2px;
  height: calc(100% - 0.5rem);
  background-color: #dee2e6;
}

/* Modal enhancements */
.modal-lg {
  max-width: 900px;
}

.modal-content {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
  background-color: var(--light-color);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
  background-color: var(--light-color);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Form validation styles */
.form-control.is-valid {
  border-color: var(--success-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38 3.02-3.02.94.94L2.3 10.27z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
  border-color: var(--danger-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Enhanced button styles */
.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.btn-outline-primary:hover,
.btn-outline-warning:hover,
.btn-outline-info:hover,
.btn-outline-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Statistics cards enhancements */
.card.border-0.shadow-sm {
  transition: all 0.3s ease;
}

.card.border-0.shadow-sm:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Search and filter enhancements */
.input-group-text {
  background-color: var(--light-color);
  border-color: #ced4da;
  color: var(--secondary-color);
}

.form-select {
  border-radius: var(--border-radius);
  border: 1px solid #ced4da;
  transition: var(--transition);
}

.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Table enhancements */
.table-light th {
  background-color: var(--light-color);
  color: var(--dark-color);
  font-weight: 600;
  border-bottom: 2px solid rgba(0, 0, 0, 0.125);
}

.table-hover tbody tr:hover {
  background-color: rgba(220, 53, 69, 0.05);
}

/* Loading and empty states */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.text-muted {
  color: #6c757d !important;
}

/* Responsive improvements for students page */
@media (max-width: 768px) {
  .students-page .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .students-page .btn {
    width: 100%;
  }

  .students-page .card-header .d-flex {
    flex-direction: column;
    align-items: stretch !important;
    gap: 1rem;
  }

  .students-page .table-responsive {
    font-size: 0.875rem;
  }

  .students-page .btn-group-sm {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .students-page .btn-group-sm .btn {
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.25rem;
  }
}

@media (max-width: 576px) {
  .students-page .modal-dialog {
    margin: 0.5rem;
  }

  .students-page .modal-lg {
    max-width: none;
  }

  .students-page .row.g-3 > .col-md-2,
  .students-page .row.g-3 > .col-md-4,
  .students-page .row.g-3 > .col-md-6 {
    margin-bottom: 1rem;
  }
}
