// Users Management Page
class UsersPage {
  constructor() {
    this.state = window.stateManager;
    this.api = window.apiServices;
    this.utils = window.utils;
    this.currentPage = 1;
    this.pageSize = 10;
    this.searchQuery = '';
    this.filters = {};
    this.sortBy = 'createdAt';
    this.sortOrder = 'desc';
    this.users = [];
    this.totalUsers = 0;
    this.isLoading = false;
    this.roles = {
      'admin': 'مدير النظام',
      'coach': 'مدرب',
      'receptionist': 'موظف استقبال'
    };
    this.permissions = {
      'read_students': 'عرض الطلاب',
      'write_students': 'إدارة الطلاب',
      'read_attendance': 'عرض الحضور',
      'write_attendance': 'تسجيل الحضور',
      'read_subscriptions': 'عرض الاشتراكات',
      'write_subscriptions': 'إدارة الاشتراكات',
      'read_reports': 'عرض التقارير',
      'manage_users': 'إدارة المستخدمين'
    };
  }

  async render(container) {
    container.innerHTML = `
      <div class="users-page">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h3 class="mb-1">إدارة المستخدمين</h3>
            <p class="text-muted mb-0">إدارة حسابات المستخدمين والصلاحيات</p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-info" id="rolesPermissionsBtn">
              <i class="fas fa-shield-alt me-2"></i>الأدوار والصلاحيات
            </button>
            <button class="btn btn-outline-secondary" id="exportUsersBtn">
              <i class="fas fa-download me-2"></i>تصدير البيانات
            </button>
            <button class="btn btn-primary" id="addUserBtn">
              <i class="fas fa-user-plus me-2"></i>إضافة مستخدم
            </button>
          </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-primary bg-gradient rounded-circle p-3">
                      <i class="fas fa-users text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">إجمالي المستخدمين</div>
                    <div class="h4 mb-0" id="totalUsersCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-success bg-gradient rounded-circle p-3">
                      <i class="fas fa-user-check text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">المستخدمون النشطون</div>
                    <div class="h4 mb-0" id="activeUsersCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-warning bg-gradient rounded-circle p-3">
                      <i class="fas fa-user-shield text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">المدراء</div>
                    <div class="h4 mb-0" id="adminUsersCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-info bg-gradient rounded-circle p-3">
                      <i class="fas fa-user-tie text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">المدربون</div>
                    <div class="h4 mb-0" id="coachUsersCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Search and Filters -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-4">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input type="text" class="form-control" id="searchInput" 
                         placeholder="البحث باسم المستخدم أو الاسم الكامل أو البريد الإلكتروني...">
                </div>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="roleFilter">
                  <option value="">جميع الأدوار</option>
                  <option value="admin">مدير النظام</option>
                  <option value="coach">مدرب</option>
                  <option value="receptionist">موظف استقبال</option>
                </select>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                  <option value="">جميع الحالات</option>
                  <option value="true">نشط</option>
                  <option value="false">غير نشط</option>
                </select>
              </div>
              <div class="col-md-3">
                <select class="form-select" id="permissionFilter">
                  <option value="">جميع الصلاحيات</option>
                  <option value="read_students">عرض الطلاب</option>
                  <option value="write_students">إدارة الطلاب</option>
                  <option value="read_attendance">عرض الحضور</option>
                  <option value="write_attendance">تسجيل الحضور</option>
                  <option value="read_subscriptions">عرض الاشتراكات</option>
                  <option value="write_subscriptions">إدارة الاشتراكات</option>
                  <option value="read_reports">عرض التقارير</option>
                  <option value="manage_users">إدارة المستخدمين</option>
                </select>
              </div>
              <div class="col-md-1">
                <button class="btn btn-outline-secondary w-100" id="clearFiltersBtn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Users Table -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة المستخدمين</h5>
            <div class="d-flex align-items-center gap-3">
              <div class="d-flex align-items-center">
                <label class="form-label mb-0 me-2">عرض:</label>
                <select class="form-select form-select-sm" id="pageSizeSelect" style="width: auto;">
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>المستخدم</th>
                    <th>الدور</th>
                    <th>الصلاحيات</th>
                    <th>آخر دخول</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody id="usersTableBody">
                  <!-- Users will be loaded here -->
                </tbody>
              </table>
            </div>
            
            <!-- Loading State -->
            <div id="loadingState" class="text-center py-5 d-none">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
              </div>
              <p class="mt-3 text-muted">جاري تحميل المستخدمين...</p>
            </div>
            
            <!-- Empty State -->
            <div id="emptyState" class="text-center py-5 d-none">
              <i class="fas fa-users fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا يوجد مستخدمون</h5>
              <p class="text-muted">لم يتم العثور على مستخدمين مطابقين للبحث أو الفلاتر المحددة</p>
              <button class="btn btn-primary" id="addFirstUserBtn">
                <i class="fas fa-user-plus me-2"></i>إضافة أول مستخدم
              </button>
            </div>
          </div>
          
          <!-- Pagination -->
          <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div class="text-muted">
                عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span> 
                من أصل <span id="totalCount">0</span> مستخدم
              </div>
              <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                  <!-- Pagination will be generated here -->
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    `;

    await this.bindEvents();
    await this.loadUsers();
    await this.loadStatistics();
  }

  async bindEvents() {
    // Search input with debounce
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.searchQuery = e.target.value;
          this.currentPage = 1;
          this.loadUsers();
        }, 300);
      });
    }

    // Filter changes
    ['roleFilter', 'statusFilter', 'permissionFilter'].forEach(filterId => {
      const filter = document.getElementById(filterId);
      if (filter) {
        filter.addEventListener('change', (e) => {
          const filterKey = filterId.replace('Filter', '');
          if (e.target.value) {
            this.filters[filterKey] = e.target.value;
          } else {
            delete this.filters[filterKey];
          }
          this.currentPage = 1;
          this.loadUsers();
        });
      }
    });

    // Clear filters
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', () => {
        this.clearFilters();
      });
    }

    // Page size change
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
      pageSizeSelect.addEventListener('change', (e) => {
        this.pageSize = parseInt(e.target.value);
        this.currentPage = 1;
        this.loadUsers();
      });
    }

    // Action buttons
    const addUserBtn = document.getElementById('addUserBtn');
    const addFirstUserBtn = document.getElementById('addFirstUserBtn');
    const rolesPermissionsBtn = document.getElementById('rolesPermissionsBtn');
    const exportUsersBtn = document.getElementById('exportUsersBtn');

    if (addUserBtn) {
      addUserBtn.addEventListener('click', () => this.showUserModal());
    }
    if (addFirstUserBtn) {
      addFirstUserBtn.addEventListener('click', () => this.showUserModal());
    }
    if (rolesPermissionsBtn) {
      rolesPermissionsBtn.addEventListener('click', () => this.showRolesPermissionsModal());
    }
    if (exportUsersBtn) {
      exportUsersBtn.addEventListener('click', () => this.exportUsers());
    }

    // Table row actions (using event delegation)
    const tableBody = document.getElementById('usersTableBody');
    if (tableBody) {
      tableBody.addEventListener('click', (e) => {
        const userId = e.target.closest('tr')?.dataset.userId;
        if (!userId) return;

        if (e.target.closest('.btn-view')) {
          this.viewUser(userId);
        } else if (e.target.closest('.btn-edit')) {
          this.editUser(userId);
        } else if (e.target.closest('.btn-reset-password')) {
          this.resetPassword(userId);
        } else if (e.target.closest('.btn-toggle')) {
          this.toggleUserStatus(userId);
        } else if (e.target.closest('.btn-delete')) {
          this.deactivateUser(userId);
        }
      });
    }

    // Pagination (using event delegation)
    const pagination = document.getElementById('pagination');
    if (pagination) {
      pagination.addEventListener('click', (e) => {
        e.preventDefault();
        const page = e.target.dataset.page;
        if (page && page !== this.currentPage.toString()) {
          this.currentPage = parseInt(page);
          this.loadUsers();
        }
      });
    }
  }

  clearFilters() {
    this.searchQuery = '';
    this.filters = {};
    this.currentPage = 1;

    // Reset form elements
    document.getElementById('searchInput').value = '';
    document.getElementById('roleFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('permissionFilter').value = '';

    this.loadUsers();
  }

  async loadUsers() {
    try {
      this.setLoading(true);

      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        sortBy: this.sortBy,
        sortOrder: this.sortOrder
      };

      if (this.searchQuery) {
        params.search = this.searchQuery;
      }

      Object.assign(params, this.filters);

      const response = await this.api.users.getAll(params);

      if (response.success) {
        this.users = response.data.users;
        this.totalUsers = response.data.pagination.totalUsers;
        this.renderUsersTable();
        this.renderPagination();
        this.updatePaginationInfo();
      } else {
        this.utils.showAlert('خطأ في تحميل المستخدمين', 'error');
      }
    } catch (error) {
      console.error('Error loading users:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  async loadStatistics() {
    try {
      const response = await this.api.users.getStats();
      if (response.success) {
        const stats = response.data;
        document.getElementById('totalUsersCount').textContent = stats.totalUsers || 0;
        document.getElementById('activeUsersCount').textContent = stats.activeUsers || 0;
        document.getElementById('adminUsersCount').textContent = stats.adminUsers || 0;
        document.getElementById('coachUsersCount').textContent = stats.coachUsers || 0;
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    const loadingState = document.getElementById('loadingState');
    const tableBody = document.getElementById('usersTableBody');

    if (loading) {
      loadingState?.classList.remove('d-none');
      tableBody?.classList.add('d-none');
    } else {
      loadingState?.classList.add('d-none');
      tableBody?.classList.remove('d-none');
    }
  }

  renderUsersTable() {
    const tableBody = document.getElementById('usersTableBody');
    const emptyState = document.getElementById('emptyState');

    if (!tableBody) return;

    if (this.users.length === 0) {
      tableBody.classList.add('d-none');
      emptyState?.classList.remove('d-none');
      return;
    }

    emptyState?.classList.add('d-none');
    tableBody.classList.remove('d-none');

    tableBody.innerHTML = this.users.map(user => `
      <tr data-user-id="${user._id}">
        <td>
          <div class="d-flex align-items-center">
            <div class="avatar-sm bg-light rounded-circle me-3 d-flex align-items-center justify-content-center">
              ${user.profileImage ?
                `<img src="${user.profileImage}" class="rounded-circle" width="40" height="40" alt="${user.fullName}">` :
                `<i class="fas fa-user text-muted"></i>`
              }
            </div>
            <div>
              <div class="fw-medium">${user.fullName}</div>
              <small class="text-muted">@${user.username}</small>
              <div class="small text-muted">${user.email}</div>
              ${user.phone ? `<div class="small text-muted">${user.phone}</div>` : ''}
            </div>
          </div>
        </td>
        <td>
          <span class="badge ${this.getRoleColor(user.role)}">
            ${this.roles[user.role] || user.role}
          </span>
        </td>
        <td>
          <div class="permissions-list">
            ${user.role === 'admin' ?
              '<span class="badge bg-warning text-dark">جميع الصلاحيات</span>' :
              user.permissions?.length > 0 ?
                user.permissions.slice(0, 2).map(perm =>
                  `<span class="badge bg-light text-dark small">${this.permissions[perm] || perm}</span>`
                ).join(' ') +
                (user.permissions.length > 2 ? ` <span class="small text-muted">+${user.permissions.length - 2} أخرى</span>` : '') :
                '<span class="text-muted">لا توجد صلاحيات</span>'
            }
          </div>
        </td>
        <td>
          ${user.lastLogin ?
            `<div class="fw-medium">${this.utils.formatDate(user.lastLogin)}</div>
             <small class="text-muted">${this.utils.formatTime(user.lastLogin)}</small>` :
            '<span class="text-muted">لم يسجل دخول</span>'
          }
        </td>
        <td>
          <div class="fw-medium">${this.utils.formatDate(user.createdAt)}</div>
          <small class="text-muted">${this.utils.formatTime(user.createdAt)}</small>
        </td>
        <td>
          <span class="badge ${user.isActive ? 'bg-success' : 'bg-secondary'}">
            ${user.isActive ? 'نشط' : 'غير نشط'}
          </span>
        </td>
        <td>
          <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary btn-view" title="عرض التفاصيل">
              <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-outline-warning btn-edit" title="تعديل">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-outline-info btn-reset-password" title="إعادة تعيين كلمة المرور">
              <i class="fas fa-key"></i>
            </button>
            <button class="btn btn-outline-${user.isActive ? 'secondary' : 'success'} btn-toggle"
                    title="${user.isActive ? 'إيقاف' : 'تفعيل'}">
              <i class="fas fa-${user.isActive ? 'pause' : 'play'}"></i>
            </button>
            ${user.role !== 'admin' ? `
              <button class="btn btn-outline-danger btn-delete" title="إلغاء التفعيل">
                <i class="fas fa-user-slash"></i>
              </button>
            ` : ''}
          </div>
        </td>
      </tr>
    `).join('');
  }

  getRoleColor(role) {
    const colors = {
      'admin': 'bg-danger',
      'coach': 'bg-primary',
      'receptionist': 'bg-info'
    };
    return colors[role] || 'bg-secondary';
  }

  renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    const totalPages = Math.ceil(this.totalUsers / this.pageSize);

    if (totalPages <= 1) {
      pagination.innerHTML = '';
      return;
    }

    let paginationHTML = '';

    // Previous button
    paginationHTML += `
      <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage - 1}">
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
    `;

    // Page numbers
    const startPage = Math.max(1, this.currentPage - 2);
    const endPage = Math.min(totalPages, this.currentPage + 2);

    if (startPage > 1) {
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="1">1</a>
        </li>
      `;
      if (startPage > 2) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
        <li class="page-item ${i === this.currentPage ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
        </li>
      `;
    }

    // Next button
    paginationHTML += `
      <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage + 1}">
          <i class="fas fa-chevron-left"></i>
        </a>
      </li>
    `;

    pagination.innerHTML = paginationHTML;
  }

  updatePaginationInfo() {
    const showingFrom = document.getElementById('showingFrom');
    const showingTo = document.getElementById('showingTo');
    const totalCount = document.getElementById('totalCount');

    if (showingFrom && showingTo && totalCount) {
      const from = (this.currentPage - 1) * this.pageSize + 1;
      const to = Math.min(this.currentPage * this.pageSize, this.totalUsers);

      showingFrom.textContent = this.totalUsers > 0 ? from : 0;
      showingTo.textContent = to;
      totalCount.textContent = this.totalUsers;
    }
  }
}

// Export for global access
window.UsersPage = UsersPage;
