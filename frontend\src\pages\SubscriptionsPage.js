// Subscriptions Management Page
class SubscriptionsPage {
  constructor() {
    this.state = window.stateManager;
    this.api = window.apiServices;
    this.utils = window.utils;
    this.currentPage = 1;
    this.pageSize = 10;
    this.searchQuery = '';
    this.filters = {};
    this.sortBy = 'startDate';
    this.sortOrder = 'desc';
    this.subscriptions = [];
    this.totalSubscriptions = 0;
    this.students = [];
    this.isLoading = false;
    this.predefinedPlans = [
      { name: 'monthly', duration: 30, sessionsPerWeek: 3, totalSessions: 12, price: 300 },
      { name: 'quarterly', duration: 90, sessionsPerWeek: 3, totalSessions: 36, price: 800 },
      { name: 'semi-annual', duration: 180, sessionsPerWeek: 3, totalSessions: 72, price: 1500 },
      { name: 'annual', duration: 365, sessionsPerWeek: 3, totalSessions: 144, price: 2800 },
      { name: 'unlimited', duration: 365, sessionsPerWeek: 7, totalSessions: 365, price: 5000 }
    ];
  }

  async render(container) {
    container.innerHTML = `
      <div class="subscriptions-page">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h3 class="mb-1">إدارة الاشتراكات</h3>
            <p class="text-muted mb-0">إدارة اشتراكات الطلاب والتجديد</p>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-warning" id="expiringSubscriptionsBtn">
              <i class="fas fa-exclamation-triangle me-2"></i>الاشتراكات المنتهية قريباً
            </button>
            <button class="btn btn-outline-secondary" id="exportSubscriptionsBtn">
              <i class="fas fa-download me-2"></i>تصدير البيانات
            </button>
            <button class="btn btn-primary" id="addSubscriptionBtn">
              <i class="fas fa-plus me-2"></i>اشتراك جديد
            </button>
          </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-success bg-gradient rounded-circle p-3">
                      <i class="fas fa-check-circle text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">الاشتراكات النشطة</div>
                    <div class="h4 mb-0" id="activeSubscriptionsCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-warning bg-gradient rounded-circle p-3">
                      <i class="fas fa-clock text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">تنتهي قريباً</div>
                    <div class="h4 mb-0" id="expiringSoonCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-danger bg-gradient rounded-circle p-3">
                      <i class="fas fa-times-circle text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">منتهية الصلاحية</div>
                    <div class="h4 mb-0" id="expiredSubscriptionsCount">0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="flex-shrink-0">
                    <div class="bg-info bg-gradient rounded-circle p-3">
                      <i class="fas fa-money-bill-wave text-white"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1 ms-3">
                    <div class="text-muted small">إجمالي الإيرادات</div>
                    <div class="h4 mb-0" id="totalRevenueAmount">0 ريال</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Search and Filters -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-3">
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input type="text" class="form-control" id="searchInput"
                         placeholder="البحث بالاسم أو رقم الاشتراك...">
                </div>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                  <option value="">جميع الحالات</option>
                  <option value="active">نشط</option>
                  <option value="expired">منتهي</option>
                  <option value="suspended">موقوف</option>
                  <option value="cancelled">ملغي</option>
                  <option value="pending">في الانتظار</option>
                </select>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="paymentStatusFilter">
                  <option value="">حالة الدفع</option>
                  <option value="paid">مدفوع</option>
                  <option value="partial">مدفوع جزئياً</option>
                  <option value="unpaid">غير مدفوع</option>
                  <option value="refunded">مسترد</option>
                </select>
              </div>
              <div class="col-md-2">
                <select class="form-select" id="planFilter">
                  <option value="">جميع الخطط</option>
                  <option value="monthly">شهري</option>
                  <option value="quarterly">ربع سنوي</option>
                  <option value="semi-annual">نصف سنوي</option>
                  <option value="annual">سنوي</option>
                  <option value="unlimited">غير محدود</option>
                </select>
              </div>
              <div class="col-md-3">
                <button class="btn btn-outline-secondary w-100" id="clearFiltersBtn">
                  <i class="fas fa-times me-2"></i>مسح الفلاتر
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Subscriptions Table -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة الاشتراكات</h5>
            <div class="d-flex align-items-center gap-3">
              <div class="d-flex align-items-center">
                <label class="form-label mb-0 me-2">عرض:</label>
                <select class="form-select form-select-sm" id="pageSizeSelect" style="width: auto;">
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>رقم الاشتراك</th>
                    <th>الطالب</th>
                    <th>الخطة</th>
                    <th>المبلغ</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>الجلسات المتبقية</th>
                    <th>حالة الدفع</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody id="subscriptionsTableBody">
                  <!-- Subscriptions will be loaded here -->
                </tbody>
              </table>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-5 d-none">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
              </div>
              <p class="mt-3 text-muted">جاري تحميل الاشتراكات...</p>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="text-center py-5 d-none">
              <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا توجد اشتراكات</h5>
              <p class="text-muted">لم يتم العثور على اشتراكات مطابقة للبحث أو الفلاتر المحددة</p>
              <button class="btn btn-primary" id="addFirstSubscriptionBtn">
                <i class="fas fa-plus me-2"></i>إضافة أول اشتراك
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div class="text-muted">
                عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span>
                من أصل <span id="totalCount">0</span> اشتراك
              </div>
              <nav>
                <ul class="pagination pagination-sm mb-0" id="pagination">
                  <!-- Pagination will be generated here -->
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    `;

    await this.bindEvents();
    await this.loadInitialData();
    await this.loadSubscriptions();
    await this.loadStatistics();
  }

  async bindEvents() {
    // Search input with debounce
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.searchQuery = e.target.value;
          this.currentPage = 1;
          this.loadSubscriptions();
        }, 300);
      });
    }

    // Filter changes
    ['statusFilter', 'paymentStatusFilter', 'planFilter'].forEach(filterId => {
      const filter = document.getElementById(filterId);
      if (filter) {
        filter.addEventListener('change', (e) => {
          const filterKey = filterId.replace('Filter', '');
          if (e.target.value) {
            this.filters[filterKey] = e.target.value;
          } else {
            delete this.filters[filterKey];
          }
          this.currentPage = 1;
          this.loadSubscriptions();
        });
      }
    });

    // Clear filters
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    if (clearFiltersBtn) {
      clearFiltersBtn.addEventListener('click', () => {
        this.clearFilters();
      });
    }

    // Page size change
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
      pageSizeSelect.addEventListener('change', (e) => {
        this.pageSize = parseInt(e.target.value);
        this.currentPage = 1;
        this.loadSubscriptions();
      });
    }

    // Action buttons
    const addSubscriptionBtn = document.getElementById('addSubscriptionBtn');
    const addFirstSubscriptionBtn = document.getElementById('addFirstSubscriptionBtn');
    const expiringSubscriptionsBtn = document.getElementById('expiringSubscriptionsBtn');
    const exportSubscriptionsBtn = document.getElementById('exportSubscriptionsBtn');

    if (addSubscriptionBtn) {
      addSubscriptionBtn.addEventListener('click', () => this.showSubscriptionModal());
    }
    if (addFirstSubscriptionBtn) {
      addFirstSubscriptionBtn.addEventListener('click', () => this.showSubscriptionModal());
    }
    if (expiringSubscriptionsBtn) {
      expiringSubscriptionsBtn.addEventListener('click', () => this.showExpiringSubscriptions());
    }
    if (exportSubscriptionsBtn) {
      exportSubscriptionsBtn.addEventListener('click', () => this.exportSubscriptions());
    }

    // Table row actions (using event delegation)
    const tableBody = document.getElementById('subscriptionsTableBody');
    if (tableBody) {
      tableBody.addEventListener('click', (e) => {
        const subscriptionId = e.target.closest('tr')?.dataset.subscriptionId;
        if (!subscriptionId) return;

        if (e.target.closest('.btn-view')) {
          this.viewSubscription(subscriptionId);
        } else if (e.target.closest('.btn-edit')) {
          this.editSubscription(subscriptionId);
        } else if (e.target.closest('.btn-renew')) {
          this.renewSubscription(subscriptionId);
        } else if (e.target.closest('.btn-suspend')) {
          this.suspendSubscription(subscriptionId);
        } else if (e.target.closest('.btn-delete')) {
          this.deleteSubscription(subscriptionId);
        }
      });
    }

    // Pagination (using event delegation)
    const pagination = document.getElementById('pagination');
    if (pagination) {
      pagination.addEventListener('click', (e) => {
        e.preventDefault();
        const page = e.target.dataset.page;
        if (page && page !== this.currentPage.toString()) {
          this.currentPage = parseInt(page);
          this.loadSubscriptions();
        }
      });
    }
  }

  clearFilters() {
    this.searchQuery = '';
    this.filters = {};
    this.currentPage = 1;

    // Reset form elements
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('paymentStatusFilter').value = '';
    document.getElementById('planFilter').value = '';

    this.loadSubscriptions();
  }

  async loadInitialData() {
    try {
      // Load students for dropdowns
      const studentsResponse = await this.api.students.getAll({ limit: 1000, status: 'active' });

      if (studentsResponse.success) {
        this.students = studentsResponse.data.students;
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  }

  async loadSubscriptions() {
    try {
      this.setLoading(true);

      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        sortBy: this.sortBy,
        sortOrder: this.sortOrder
      };

      if (this.searchQuery) {
        params.search = this.searchQuery;
      }

      Object.assign(params, this.filters);

      const response = await this.api.subscriptions.getAll(params);

      if (response.success) {
        this.subscriptions = response.data.subscriptions;
        this.totalSubscriptions = response.data.total;
        this.renderSubscriptionsTable();
        this.renderPagination();
        this.updatePaginationInfo();
      } else {
        this.utils.showAlert('خطأ في تحميل الاشتراكات', 'error');
      }
    } catch (error) {
      console.error('Error loading subscriptions:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  async loadStatistics() {
    try {
      const response = await this.api.subscriptions.getStats();
      if (response.success) {
        const stats = response.data.overview;
        document.getElementById('activeSubscriptionsCount').textContent = stats.activeSubscriptions || 0;
        document.getElementById('expiringSoonCount').textContent = stats.expiringSoon || 0;
        document.getElementById('expiredSubscriptionsCount').textContent = stats.expiredSubscriptions || 0;
        document.getElementById('totalRevenueAmount').textContent = `${stats.totalRevenue || 0} ريال`;
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    const loadingState = document.getElementById('loadingState');
    const tableBody = document.getElementById('subscriptionsTableBody');

    if (loading) {
      loadingState?.classList.remove('d-none');
      tableBody?.classList.add('d-none');
    } else {
      loadingState?.classList.add('d-none');
      tableBody?.classList.remove('d-none');
    }
  }

  renderSubscriptionsTable() {
    const tableBody = document.getElementById('subscriptionsTableBody');
    const emptyState = document.getElementById('emptyState');

    if (!tableBody) return;

    if (this.subscriptions.length === 0) {
      tableBody.classList.add('d-none');
      emptyState?.classList.remove('d-none');
      return;
    }

    emptyState?.classList.add('d-none');
    tableBody.classList.remove('d-none');

    tableBody.innerHTML = this.subscriptions.map(subscription => `
      <tr data-subscription-id="${subscription._id}">
        <td>
          <span class="fw-medium">${subscription.subscriptionId}</span>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
              <i class="fas fa-user text-muted"></i>
            </div>
            <div>
              <div class="fw-medium">${subscription.student?.fullName || 'غير محدد'}</div>
              <small class="text-muted">${subscription.student?.studentId || ''}</small>
            </div>
          </div>
        </td>
        <td>
          <div>
            <span class="badge ${this.getPlanColor(subscription.plan?.name)}">
              ${this.getPlanText(subscription.plan?.name)}
            </span>
            <div class="small text-muted mt-1">
              ${subscription.plan?.sessionsPerWeek} جلسات/أسبوع
            </div>
          </div>
        </td>
        <td>
          <div class="fw-medium">${subscription.finalAmount || subscription.amount} ريال</div>
          ${subscription.discount?.value > 0 ? `
            <small class="text-success">
              خصم ${subscription.discount.value}${subscription.discount.type === 'percentage' ? '%' : ' ريال'}
            </small>
          ` : ''}
        </td>
        <td>
          <div class="fw-medium">${this.utils.formatDate(subscription.startDate)}</div>
        </td>
        <td>
          <div class="fw-medium">${this.utils.formatDate(subscription.endDate)}</div>
          <small class="text-muted">
            ${subscription.daysRemaining > 0 ? `${subscription.daysRemaining} يوم متبقي` : 'منتهي'}
          </small>
        </td>
        <td>
          <div class="d-flex align-items-center">
            <div class="progress me-2" style="width: 60px; height: 8px;">
              <div class="progress-bar ${subscription.usagePercentage > 80 ? 'bg-danger' : subscription.usagePercentage > 60 ? 'bg-warning' : 'bg-success'}"
                   style="width: ${subscription.usagePercentage}%"></div>
            </div>
            <small class="fw-medium">${subscription.remainingSessions}/${subscription.plan?.totalSessions}</small>
          </div>
        </td>
        <td>
          <span class="badge ${this.getPaymentStatusColor(subscription.paymentStatus)}">
            ${this.getPaymentStatusText(subscription.paymentStatus)}
          </span>
        </td>
        <td>
          <span class="badge ${this.getStatusColor(subscription.status)}">
            ${this.getStatusText(subscription.status)}
          </span>
        </td>
        <td>
          <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary btn-view" title="عرض التفاصيل">
              <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-outline-warning btn-edit" title="تعديل">
              <i class="fas fa-edit"></i>
            </button>
            ${subscription.status === 'active' ? `
              <button class="btn btn-outline-success btn-renew" title="تجديد">
                <i class="fas fa-redo"></i>
              </button>
              <button class="btn btn-outline-secondary btn-suspend" title="إيقاف">
                <i class="fas fa-pause"></i>
              </button>
            ` : subscription.status === 'expired' ? `
              <button class="btn btn-outline-success btn-renew" title="تجديد">
                <i class="fas fa-redo"></i>
              </button>
            ` : ''}
            <button class="btn btn-outline-danger btn-delete" title="حذف">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `).join('');
  }

  getPlanColor(planName) {
    const colors = {
      'monthly': 'bg-primary',
      'quarterly': 'bg-success',
      'semi-annual': 'bg-warning',
      'annual': 'bg-info',
      'unlimited': 'bg-danger'
    };
    return colors[planName] || 'bg-secondary';
  }

  getPlanText(planName) {
    const texts = {
      'monthly': 'شهري',
      'quarterly': 'ربع سنوي',
      'semi-annual': 'نصف سنوي',
      'annual': 'سنوي',
      'unlimited': 'غير محدود'
    };
    return texts[planName] || planName;
  }

  getStatusColor(status) {
    const colors = {
      'active': 'bg-success',
      'expired': 'bg-danger',
      'suspended': 'bg-warning',
      'cancelled': 'bg-secondary',
      'pending': 'bg-info'
    };
    return colors[status] || 'bg-secondary';
  }

  getStatusText(status) {
    const texts = {
      'active': 'نشط',
      'expired': 'منتهي',
      'suspended': 'موقوف',
      'cancelled': 'ملغي',
      'pending': 'في الانتظار'
    };
    return texts[status] || status;
  }

  getPaymentStatusColor(paymentStatus) {
    const colors = {
      'paid': 'bg-success',
      'partial': 'bg-warning',
      'unpaid': 'bg-danger',
      'refunded': 'bg-info'
    };
    return colors[paymentStatus] || 'bg-secondary';
  }

  getPaymentStatusText(paymentStatus) {
    const texts = {
      'paid': 'مدفوع',
      'partial': 'مدفوع جزئياً',
      'unpaid': 'غير مدفوع',
      'refunded': 'مسترد'
    };
    return texts[paymentStatus] || paymentStatus;
  }

  renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    const totalPages = Math.ceil(this.totalSubscriptions / this.pageSize);

    if (totalPages <= 1) {
      pagination.innerHTML = '';
      return;
    }

    let paginationHTML = '';

    // Previous button
    paginationHTML += `
      <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage - 1}">
          <i class="fas fa-chevron-right"></i>
        </a>
      </li>
    `;

    // Page numbers
    const startPage = Math.max(1, this.currentPage - 2);
    const endPage = Math.min(totalPages, this.currentPage + 2);

    if (startPage > 1) {
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="1">1</a>
        </li>
      `;
      if (startPage > 2) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
        <li class="page-item ${i === this.currentPage ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
      }
      paginationHTML += `
        <li class="page-item">
          <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
        </li>
      `;
    }

    // Next button
    paginationHTML += `
      <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${this.currentPage + 1}">
          <i class="fas fa-chevron-left"></i>
        </a>
      </li>
    `;

    pagination.innerHTML = paginationHTML;
  }

  updatePaginationInfo() {
    const showingFrom = document.getElementById('showingFrom');
    const showingTo = document.getElementById('showingTo');
    const totalCount = document.getElementById('totalCount');

    if (showingFrom && showingTo && totalCount) {
      const from = (this.currentPage - 1) * this.pageSize + 1;
      const to = Math.min(this.currentPage * this.pageSize, this.totalSubscriptions);

      showingFrom.textContent = this.totalSubscriptions > 0 ? from : 0;
      showingTo.textContent = to;
      totalCount.textContent = this.totalSubscriptions;
    }
  }

  // CRUD Operations
  async showSubscriptionModal(subscription = null) {
    const isEdit = !!subscription;
    const modalTitle = isEdit ? 'تعديل الاشتراك' : 'اشتراك جديد';

    const modalHTML = `
      <div class="modal fade" id="subscriptionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${modalTitle}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="subscriptionForm" novalidate>
                <div class="row">
                  <!-- Student Selection -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">الطالب <span class="text-danger">*</span></label>
                    <select class="form-select" name="student" required ${isEdit ? 'disabled' : ''}>
                      <option value="">اختر الطالب</option>
                      ${this.students.map(student => `
                        <option value="${student._id}" ${subscription?.student?._id === student._id ? 'selected' : ''}>
                          ${student.fullName} (${student.studentId})
                        </option>
                      `).join('')}
                    </select>
                    <div class="invalid-feedback">يرجى اختيار الطالب</div>
                  </div>

                  <!-- Plan Selection -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">نوع الخطة <span class="text-danger">*</span></label>
                    <select class="form-select" name="planName" required id="planSelect">
                      <option value="">اختر نوع الخطة</option>
                      ${this.predefinedPlans.map(plan => `
                        <option value="${plan.name}"
                                data-duration="${plan.duration}"
                                data-sessions-per-week="${plan.sessionsPerWeek}"
                                data-total-sessions="${plan.totalSessions}"
                                data-price="${plan.price}"
                                ${subscription?.plan?.name === plan.name ? 'selected' : ''}>
                          ${this.getPlanText(plan.name)} - ${plan.price} ريال
                        </option>
                      `).join('')}
                    </select>
                    <div class="invalid-feedback">يرجى اختيار نوع الخطة</div>
                  </div>

                  <!-- Plan Details -->
                  <div class="col-md-4 mb-3">
                    <label class="form-label">مدة الاشتراك (أيام) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="planDuration" required min="1" max="365"
                           value="${subscription?.plan?.duration || ''}">
                    <div class="invalid-feedback">مدة الاشتراك مطلوبة</div>
                  </div>

                  <div class="col-md-4 mb-3">
                    <label class="form-label">جلسات/أسبوع <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="sessionsPerWeek" required min="1" max="7"
                           value="${subscription?.plan?.sessionsPerWeek || ''}">
                    <div class="invalid-feedback">عدد الجلسات مطلوب</div>
                  </div>

                  <div class="col-md-4 mb-3">
                    <label class="form-label">إجمالي الجلسات <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="totalSessions" required min="1"
                           value="${subscription?.plan?.totalSessions || ''}">
                    <div class="invalid-feedback">إجمالي الجلسات مطلوب</div>
                  </div>

                  <!-- Pricing -->
                  <div class="col-md-6 mb-3">
                    <label class="form-label">المبلغ الأساسي (ريال) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="amount" required min="0" step="0.01"
                           value="${subscription?.amount || ''}">
                    <div class="invalid-feedback">المبلغ مطلوب</div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" name="startDate" required
                           value="${subscription?.startDate ? new Date(subscription.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]}">
                    <div class="invalid-feedback">تاريخ البداية مطلوب</div>
                  </div>

                  <!-- Discount Section -->
                  <div class="col-12 mt-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-percent me-2"></i>الخصم (اختياري)
                    </h6>
                  </div>

                  <div class="col-md-4 mb-3">
                    <label class="form-label">نوع الخصم</label>
                    <select class="form-select" name="discountType">
                      <option value="">بدون خصم</option>
                      <option value="percentage" ${subscription?.discount?.type === 'percentage' ? 'selected' : ''}>نسبة مئوية</option>
                      <option value="fixed" ${subscription?.discount?.type === 'fixed' ? 'selected' : ''}>مبلغ ثابت</option>
                    </select>
                  </div>

                  <div class="col-md-4 mb-3">
                    <label class="form-label">قيمة الخصم</label>
                    <input type="number" class="form-control" name="discountValue" min="0" step="0.01"
                           value="${subscription?.discount?.value || ''}">
                  </div>

                  <div class="col-md-4 mb-3">
                    <label class="form-label">سبب الخصم</label>
                    <input type="text" class="form-control" name="discountReason" maxlength="100"
                           value="${subscription?.discount?.reason || ''}" placeholder="سبب الخصم...">
                  </div>

                  <!-- Additional Options -->
                  <div class="col-12 mt-3">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-cog me-2"></i>خيارات إضافية
                    </h6>
                  </div>

                  <div class="col-md-6 mb-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" name="autoRenewal"
                             ${subscription?.autoRenewal ? 'checked' : ''}>
                      <label class="form-check-label">
                        التجديد التلقائي
                      </label>
                    </div>
                    <div class="form-text">سيتم تجديد الاشتراك تلقائياً عند انتهائه</div>
                  </div>

                  <div class="col-12 mb-3">
                    <label class="form-label">ملاحظات</label>
                    <textarea class="form-control" name="notes" rows="3" maxlength="500"
                              placeholder="أي ملاحظات إضافية...">${subscription?.notes || ''}</textarea>
                    <div class="form-text">الحد الأقصى 500 حرف</div>
                  </div>

                  <!-- Price Summary -->
                  <div class="col-12">
                    <div class="card bg-light">
                      <div class="card-body">
                        <h6 class="card-title">ملخص السعر</h6>
                        <div class="d-flex justify-content-between">
                          <span>المبلغ الأساسي:</span>
                          <span id="baseAmount">0 ريال</span>
                        </div>
                        <div class="d-flex justify-content-between text-success" id="discountRow" style="display: none;">
                          <span>الخصم:</span>
                          <span id="discountAmount">0 ريال</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                          <span>المبلغ النهائي:</span>
                          <span id="finalAmount">0 ريال</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-primary" id="saveSubscriptionBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                ${isEdit ? 'تحديث' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('subscriptionModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('subscriptionModal'));
    modal.show();

    // Bind form events
    this.bindSubscriptionFormEvents(isEdit, subscription);
  }

  bindSubscriptionFormEvents(isEdit, subscription) {
    const form = document.getElementById('subscriptionForm');
    const saveBtn = document.getElementById('saveSubscriptionBtn');
    const planSelect = document.getElementById('planSelect');

    if (!form || !saveBtn) return;

    // Plan selection change handler
    planSelect.addEventListener('change', (e) => {
      const selectedOption = e.target.selectedOptions[0];
      if (selectedOption && selectedOption.value) {
        // Auto-fill plan details
        form.querySelector('[name="planDuration"]').value = selectedOption.dataset.duration;
        form.querySelector('[name="sessionsPerWeek"]').value = selectedOption.dataset.sessionsPerWeek;
        form.querySelector('[name="totalSessions"]').value = selectedOption.dataset.totalSessions;
        form.querySelector('[name="amount"]').value = selectedOption.dataset.price;

        this.updatePriceSummary();
      }
    });

    // Amount and discount change handlers
    ['amount', 'discountType', 'discountValue'].forEach(fieldName => {
      const field = form.querySelector(`[name="${fieldName}"]`);
      if (field) {
        field.addEventListener('input', () => this.updatePriceSummary());
        field.addEventListener('change', () => this.updatePriceSummary());
      }
    });

    // Form validation
    form.addEventListener('input', (e) => {
      this.validateSubscriptionField(e.target);
    });

    // Save button click
    saveBtn.addEventListener('click', async () => {
      if (this.validateSubscriptionForm(form)) {
        await this.saveSubscription(form, isEdit, subscription);
      }
    });

    // Form submit
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      if (this.validateSubscriptionForm(form)) {
        await this.saveSubscription(form, isEdit, subscription);
      }
    });

    // Initial price calculation
    this.updatePriceSummary();
  }

  updatePriceSummary() {
    const form = document.getElementById('subscriptionForm');
    if (!form) return;

    const amount = parseFloat(form.querySelector('[name="amount"]').value) || 0;
    const discountType = form.querySelector('[name="discountType"]').value;
    const discountValue = parseFloat(form.querySelector('[name="discountValue"]').value) || 0;

    document.getElementById('baseAmount').textContent = `${amount} ريال`;

    let discountAmount = 0;
    let finalAmount = amount;

    if (discountType && discountValue > 0) {
      if (discountType === 'percentage') {
        discountAmount = amount * (discountValue / 100);
      } else {
        discountAmount = discountValue;
      }
      finalAmount = Math.max(0, amount - discountAmount);

      document.getElementById('discountRow').style.display = 'flex';
      document.getElementById('discountAmount').textContent = `-${discountAmount.toFixed(2)} ريال`;
    } else {
      document.getElementById('discountRow').style.display = 'none';
    }

    document.getElementById('finalAmount').textContent = `${finalAmount.toFixed(2)} ريال`;
  }

  validateSubscriptionField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      message = 'هذا الحقل مطلوب';
    }

    // Number validation
    if (field.type === 'number' && value) {
      const num = parseFloat(value);
      const min = parseFloat(field.min);
      const max = parseFloat(field.max);

      if (isNaN(num)) {
        isValid = false;
        message = 'يجب إدخال رقم صحيح';
      } else if (min !== undefined && num < min) {
        isValid = false;
        message = `القيمة يجب أن تكون ${min} أو أكثر`;
      } else if (max !== undefined && num > max) {
        isValid = false;
        message = `القيمة يجب أن تكون ${max} أو أقل`;
      }
    }

    // Date validation
    if (field.type === 'date' && value) {
      const date = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (date < today) {
        isValid = false;
        message = 'تاريخ البداية لا يمكن أن يكون في الماضي';
      }
    }

    // Update field state
    if (isValid) {
      field.classList.remove('is-invalid');
      field.classList.add('is-valid');
    } else {
      field.classList.remove('is-valid');
      field.classList.add('is-invalid');
      const feedback = field.nextElementSibling;
      if (feedback && feedback.classList.contains('invalid-feedback')) {
        feedback.textContent = message;
      }
    }

    return isValid;
  }

  validateSubscriptionForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!this.validateSubscriptionField(field)) {
        isValid = false;
      }
    });

    return isValid;
  }

  async saveSubscription(form, isEdit, existingSubscription) {
    const saveBtn = document.getElementById('saveSubscriptionBtn');
    const spinner = saveBtn.querySelector('.spinner-border');

    try {
      // Show loading
      saveBtn.disabled = true;
      spinner?.classList.remove('d-none');

      // Collect form data
      const formData = new FormData(form);
      const subscriptionData = {
        student: formData.get('student'),
        plan: {
          name: formData.get('planName'),
          duration: parseInt(formData.get('planDuration')),
          sessionsPerWeek: parseInt(formData.get('sessionsPerWeek')),
          totalSessions: parseInt(formData.get('totalSessions'))
        },
        amount: parseFloat(formData.get('amount')),
        startDate: formData.get('startDate'),
        autoRenewal: formData.has('autoRenewal'),
        notes: formData.get('notes')
      };

      // Add discount if specified
      const discountType = formData.get('discountType');
      const discountValue = parseFloat(formData.get('discountValue'));
      if (discountType && discountValue > 0) {
        subscriptionData.discount = {
          type: discountType,
          value: discountValue,
          reason: formData.get('discountReason') || ''
        };
      }

      // API call
      let response;
      if (isEdit) {
        response = await this.api.subscriptions.update(existingSubscription._id, subscriptionData);
      } else {
        response = await this.api.subscriptions.create(subscriptionData);
      }

      if (response.success) {
        this.utils.showAlert(
          isEdit ? 'تم تحديث الاشتراك بنجاح' : 'تم إنشاء الاشتراك بنجاح',
          'success'
        );

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('subscriptionModal'));
        modal.hide();

        // Reload data
        await this.loadSubscriptions();
        await this.loadStatistics();
      } else {
        this.utils.showAlert(response.message || 'حدث خطأ أثناء الحفظ', 'error');
      }
    } catch (error) {
      console.error('Error saving subscription:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    } finally {
      // Hide loading
      saveBtn.disabled = false;
      spinner?.classList.add('d-none');
    }
  }

  async viewSubscription(subscriptionId) {
    try {
      const response = await this.api.subscriptions.getById(subscriptionId);
      if (response.success) {
        this.showSubscriptionDetailsModal(response.data.subscription);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الاشتراك', 'error');
      }
    } catch (error) {
      console.error('Error loading subscription:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  }

  async editSubscription(subscriptionId) {
    try {
      const response = await this.api.subscriptions.getById(subscriptionId);
      if (response.success) {
        this.showSubscriptionModal(response.data.subscription);
      } else {
        this.utils.showAlert('خطأ في تحميل بيانات الاشتراك', 'error');
      }
    } catch (error) {
      console.error('Error loading subscription:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  }

  async renewSubscription(subscriptionId) {
    try {
      const response = await this.api.subscriptions.getById(subscriptionId);
      if (!response.success) {
        this.utils.showAlert('خطأ في تحميل بيانات الاشتراك', 'error');
        return;
      }

      const subscription = response.data.subscription;
      this.showRenewalModal(subscription);
    } catch (error) {
      console.error('Error loading subscription:', error);
      this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
    }
  }

  async suspendSubscription(subscriptionId) {
    const result = await this.utils.showConfirm(
      'تأكيد الإيقاف',
      'هل أنت متأكد من إيقاف هذا الاشتراك؟',
      'إيقاف',
      'إلغاء'
    );

    if (result) {
      try {
        const response = await this.api.subscriptions.suspend(subscriptionId);
        if (response.success) {
          this.utils.showAlert('تم إيقاف الاشتراك بنجاح', 'success');
          await this.loadSubscriptions();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || 'خطأ في إيقاف الاشتراك', 'error');
        }
      } catch (error) {
        console.error('Error suspending subscription:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      }
    }
  }

  async deleteSubscription(subscriptionId) {
    const result = await this.utils.showConfirm(
      'تأكيد الحذف',
      'هل أنت متأكد من حذف هذا الاشتراك؟ هذا الإجراء لا يمكن التراجع عنه.',
      'حذف',
      'إلغاء'
    );

    if (result) {
      try {
        const response = await this.api.subscriptions.delete(subscriptionId);
        if (response.success) {
          this.utils.showAlert('تم حذف الاشتراك بنجاح', 'success');
          await this.loadSubscriptions();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || 'خطأ في حذف الاشتراك', 'error');
        }
      } catch (error) {
        console.error('Error deleting subscription:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      }
    }
  }

  showSubscriptionDetailsModal(subscription) {
    const modalHTML = `
      <div class="modal fade" id="subscriptionDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">تفاصيل الاشتراك</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                <!-- Subscription Header -->
                <div class="col-12 mb-4">
                  <div class="text-center">
                    <div class="avatar-xl bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                      <i class="fas fa-file-contract fa-3x text-muted"></i>
                    </div>
                    <h4>${subscription.subscriptionId}</h4>
                    <div class="d-flex justify-content-center gap-2 mt-2">
                      <span class="badge ${this.getPlanColor(subscription.plan?.name)}">${this.getPlanText(subscription.plan?.name)}</span>
                      <span class="badge ${this.getStatusColor(subscription.status)}">${this.getStatusText(subscription.status)}</span>
                      <span class="badge ${this.getPaymentStatusColor(subscription.paymentStatus)}">${this.getPaymentStatusText(subscription.paymentStatus)}</span>
                    </div>
                  </div>
                </div>

                <!-- Student Info -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">معلومات الطالب</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">الاسم:</td>
                      <td>${subscription.student?.fullName || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">رقم الطالب:</td>
                      <td>${subscription.student?.studentId || 'غير محدد'}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">الهاتف:</td>
                      <td>${subscription.student?.phone || 'غير محدد'}</td>
                    </tr>
                  </table>
                </div>

                <!-- Plan Details -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">تفاصيل الخطة</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">نوع الخطة:</td>
                      <td><span class="badge ${this.getPlanColor(subscription.plan?.name)}">${this.getPlanText(subscription.plan?.name)}</span></td>
                    </tr>
                    <tr>
                      <td class="fw-medium">المدة:</td>
                      <td>${subscription.plan?.duration} يوم</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">جلسات/أسبوع:</td>
                      <td>${subscription.plan?.sessionsPerWeek}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">إجمالي الجلسات:</td>
                      <td>${subscription.plan?.totalSessions}</td>
                    </tr>
                  </table>
                </div>

                <!-- Subscription Period -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">فترة الاشتراك</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">تاريخ البداية:</td>
                      <td>${this.utils.formatDate(subscription.startDate)}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">تاريخ النهاية:</td>
                      <td>${this.utils.formatDate(subscription.endDate)}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">الأيام المتبقية:</td>
                      <td class="${subscription.daysRemaining <= 7 ? 'text-danger' : subscription.daysRemaining <= 30 ? 'text-warning' : 'text-success'}">
                        ${subscription.daysRemaining > 0 ? `${subscription.daysRemaining} يوم` : 'منتهي'}
                      </td>
                    </tr>
                  </table>
                </div>

                <!-- Usage & Payment -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">الاستخدام والدفع</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">الجلسات المستخدمة:</td>
                      <td>${subscription.sessionsUsed}/${subscription.plan?.totalSessions}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">الجلسات المتبقية:</td>
                      <td class="${subscription.remainingSessions <= 5 ? 'text-danger' : 'text-success'}">
                        ${subscription.remainingSessions}
                      </td>
                    </tr>
                    <tr>
                      <td class="fw-medium">نسبة الاستخدام:</td>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="progress me-2" style="width: 100px; height: 8px;">
                            <div class="progress-bar ${subscription.usagePercentage > 80 ? 'bg-danger' : subscription.usagePercentage > 60 ? 'bg-warning' : 'bg-success'}"
                                 style="width: ${subscription.usagePercentage}%"></div>
                          </div>
                          <small>${subscription.usagePercentage}%</small>
                        </div>
                      </td>
                    </tr>
                  </table>
                </div>

                <!-- Pricing -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">التسعير</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">المبلغ الأساسي:</td>
                      <td>${subscription.amount} ريال</td>
                    </tr>
                    ${subscription.discount?.value > 0 ? `
                      <tr>
                        <td class="fw-medium">الخصم:</td>
                        <td class="text-success">
                          ${subscription.discount.value}${subscription.discount.type === 'percentage' ? '%' : ' ريال'}
                          ${subscription.discount.reason ? `(${subscription.discount.reason})` : ''}
                        </td>
                      </tr>
                    ` : ''}
                    <tr>
                      <td class="fw-medium">المبلغ النهائي:</td>
                      <td class="fw-bold">${subscription.finalAmount || subscription.amount} ريال</td>
                    </tr>
                  </table>
                </div>

                <!-- Additional Info -->
                <div class="col-md-6 mb-4">
                  <h6 class="text-primary mb-3">معلومات إضافية</h6>
                  <table class="table table-sm">
                    <tr>
                      <td class="fw-medium">التجديد التلقائي:</td>
                      <td>
                        <span class="badge ${subscription.autoRenewal ? 'bg-success' : 'bg-secondary'}">
                          ${subscription.autoRenewal ? 'مفعل' : 'غير مفعل'}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td class="fw-medium">تاريخ الإنشاء:</td>
                      <td>${this.utils.formatDateTime(subscription.createdAt)}</td>
                    </tr>
                    <tr>
                      <td class="fw-medium">أنشئ بواسطة:</td>
                      <td>${subscription.createdBy?.fullName || 'غير محدد'}</td>
                    </tr>
                  </table>
                </div>

                <!-- Notes -->
                ${subscription.notes ? `
                  <div class="col-12 mb-3">
                    <h6 class="text-primary mb-3">ملاحظات</h6>
                    <div class="bg-light p-3 rounded">
                      ${subscription.notes}
                    </div>
                  </div>
                ` : ''}
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
              ${subscription.status === 'active' || subscription.status === 'expired' ? `
                <button type="button" class="btn btn-success" onclick="window.subscriptionsPage.renewSubscription('${subscription._id}')">
                  <i class="fas fa-redo me-2"></i>تجديد
                </button>
              ` : ''}
              <button type="button" class="btn btn-warning" onclick="window.subscriptionsPage.editSubscription('${subscription._id}')">
                <i class="fas fa-edit me-2"></i>تعديل
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('subscriptionDetailsModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('subscriptionDetailsModal'));
    modal.show();
  }

  showRenewalModal(subscription) {
    const modalHTML = `
      <div class="modal fade" id="renewalModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">تجديد الاشتراك</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="text-center mb-4">
                <div class="avatar-lg bg-light rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                  <i class="fas fa-redo fa-2x text-success"></i>
                </div>
                <h6>${subscription.student?.fullName}</h6>
                <p class="text-muted">الاشتراك الحالي: ${subscription.subscriptionId}</p>
              </div>

              <form id="renewalForm">
                <div class="mb-3">
                  <label class="form-label">نوع الخطة الجديدة <span class="text-danger">*</span></label>
                  <select class="form-select" name="planName" required>
                    ${this.predefinedPlans.map(plan => `
                      <option value="${plan.name}"
                              data-duration="${plan.duration}"
                              data-sessions-per-week="${plan.sessionsPerWeek}"
                              data-total-sessions="${plan.totalSessions}"
                              data-price="${plan.price}"
                              ${subscription.plan?.name === plan.name ? 'selected' : ''}>
                        ${this.getPlanText(plan.name)} - ${plan.price} ريال
                      </option>
                    `).join('')}
                  </select>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label">المبلغ (ريال) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="amount" required min="0" step="0.01"
                           value="${subscription.amount}">
                  </div>

                  <div class="col-md-6 mb-3">
                    <label class="form-label">خصم (ريال)</label>
                    <input type="number" class="form-control" name="discount" min="0" step="0.01" value="0">
                  </div>
                </div>

                <div class="mb-3">
                  <label class="form-label">ملاحظات</label>
                  <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات حول التجديد..."></textarea>
                </div>

                <div class="alert alert-info">
                  <i class="fas fa-info-circle me-2"></i>
                  سيبدأ الاشتراك الجديد من تاريخ اليوم وسيتم إلغاء الاشتراك الحالي.
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
              <button type="button" class="btn btn-success" id="confirmRenewalBtn">
                <span class="spinner-border spinner-border-sm d-none me-2"></span>
                تجديد الاشتراك
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('renewalModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Initialize modal
    const modal = new bootstrap.Modal(document.getElementById('renewalModal'));
    modal.show();

    // Bind renewal events
    this.bindRenewalEvents(subscription);
  }

  bindRenewalEvents(subscription) {
    const form = document.getElementById('renewalForm');
    const confirmBtn = document.getElementById('confirmRenewalBtn');
    const planSelect = form.querySelector('[name="planName"]');

    // Plan selection change handler
    planSelect.addEventListener('change', (e) => {
      const selectedOption = e.target.selectedOptions[0];
      if (selectedOption && selectedOption.value) {
        form.querySelector('[name="amount"]').value = selectedOption.dataset.price;
      }
    });

    // Confirm renewal
    confirmBtn.addEventListener('click', async () => {
      const formData = new FormData(form);
      const planName = formData.get('planName');
      const amount = parseFloat(formData.get('amount'));
      const discount = parseFloat(formData.get('discount')) || 0;
      const notes = formData.get('notes');

      if (!planName || !amount) {
        this.utils.showAlert('يرجى تحديد نوع الخطة والمبلغ', 'error');
        return;
      }

      try {
        confirmBtn.disabled = true;
        confirmBtn.querySelector('.spinner-border')?.classList.remove('d-none');

        const selectedPlan = this.predefinedPlans.find(p => p.name === planName);
        const renewalData = {
          plan: {
            name: planName,
            duration: selectedPlan.duration,
            sessionsPerWeek: selectedPlan.sessionsPerWeek,
            totalSessions: selectedPlan.totalSessions
          },
          amount,
          discount: discount > 0 ? { type: 'fixed', value: discount } : null,
          notes
        };

        const response = await this.api.subscriptions.renew(subscription._id, renewalData);

        if (response.success) {
          this.utils.showAlert('تم تجديد الاشتراك بنجاح', 'success');

          // Close modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('renewalModal'));
          modal.hide();

          // Reload data
          await this.loadSubscriptions();
          await this.loadStatistics();
        } else {
          this.utils.showAlert(response.message || 'خطأ في تجديد الاشتراك', 'error');
        }
      } catch (error) {
        console.error('Error renewing subscription:', error);
        this.utils.showAlert('خطأ في الاتصال بالخادم', 'error');
      } finally {
        confirmBtn.disabled = false;
        confirmBtn.querySelector('.spinner-border')?.classList.add('d-none');
      }
    });
  }

  async showExpiringSubscriptions() {
    try {
      const response = await this.api.subscriptions.getAll({
        expiringSoon: 30, // Next 30 days
        limit: 100
      });

      if (response.success) {
        const expiring = response.data.subscriptions;

        if (expiring.length === 0) {
          this.utils.showAlert('لا توجد اشتراكات تنتهي في الثلاثين يوماً القادمة', 'info');
          return;
        }

        // Show expiring subscriptions in a modal
        const modalHTML = `
          <div class="modal fade" id="expiringModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">الاشتراكات المنتهية قريباً</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <div class="table-responsive">
                    <table class="table table-sm">
                      <thead>
                        <tr>
                          <th>الطالب</th>
                          <th>رقم الاشتراك</th>
                          <th>تاريخ الانتهاء</th>
                          <th>الأيام المتبقية</th>
                          <th>الإجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        ${expiring.map(sub => `
                          <tr>
                            <td>${sub.student?.fullName}</td>
                            <td>${sub.subscriptionId}</td>
                            <td>${this.utils.formatDate(sub.endDate)}</td>
                            <td>
                              <span class="badge ${sub.daysRemaining <= 7 ? 'bg-danger' : 'bg-warning'}">
                                ${sub.daysRemaining} يوم
                              </span>
                            </td>
                            <td>
                              <button class="btn btn-sm btn-success" onclick="window.subscriptionsPage.renewSubscription('${sub._id}')">
                                <i class="fas fa-redo"></i> تجديد
                              </button>
                            </td>
                          </tr>
                        `).join('')}
                      </tbody>
                    </table>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('expiringModal');
        if (existingModal) {
          existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Initialize modal
        const modal = new bootstrap.Modal(document.getElementById('expiringModal'));
        modal.show();
      }
    } catch (error) {
      console.error('Error loading expiring subscriptions:', error);
      this.utils.showAlert('خطأ في تحميل الاشتراكات المنتهية', 'error');
    }
  }

  async exportSubscriptions() {
    try {
      const params = {
        search: this.searchQuery,
        ...this.filters
      };

      // Get all subscriptions for export
      const response = await this.api.subscriptions.getAll({ ...params, limit: 1000 });

      if (!response.success) {
        this.utils.showAlert('خطأ في تصدير البيانات', 'error');
        return;
      }

      const subscriptions = response.data.subscriptions;

      // Prepare CSV data
      const csvData = [
        ['رقم الاشتراك', 'الطالب', 'رقم الطالب', 'نوع الخطة', 'المبلغ', 'تاريخ البداية', 'تاريخ النهاية', 'الجلسات المستخدمة', 'الجلسات المتبقية', 'حالة الدفع', 'الحالة']
      ];

      subscriptions.forEach(sub => {
        csvData.push([
          sub.subscriptionId,
          sub.student?.fullName || '',
          sub.student?.studentId || '',
          this.getPlanText(sub.plan?.name),
          sub.finalAmount || sub.amount,
          this.utils.formatDate(sub.startDate),
          this.utils.formatDate(sub.endDate),
          sub.sessionsUsed,
          sub.remainingSessions,
          this.getPaymentStatusText(sub.paymentStatus),
          this.getStatusText(sub.status)
        ]);
      });

      // Convert to CSV and download
      const csv = csvData.map(row => row.join(',')).join('\n');
      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `subscriptions_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.utils.showAlert('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
      console.error('Error exporting subscriptions:', error);
      this.utils.showAlert('خطأ في تصدير البيانات', 'error');
    }
  }
}

// Export for global access
window.SubscriptionsPage = SubscriptionsPage;